// Firebase client-side configuration
import { initializeApp, getApps } from 'firebase/app'
import { getAuth } from 'firebase/auth'
import { getFirestore } from 'firebase/firestore'
import { getDatabase } from 'firebase/database'
import { getAnalytics } from 'firebase/analytics'

const firebaseConfig = {
  apiKey: "AIzaSyBYTl5SiY2ARvKUCiMBxf8zNUBQRu3hg1s",
  authDomain: "thepaperbull-144.firebaseapp.com",
  databaseURL: "https://thepaperbull-144-default-rtdb.firebaseio.com/",
  projectId: "thepaperbull-144",
  storageBucket: "thepaperbull-144.firebasestorage.app",
  messagingSenderId: "540770032311",
  appId: "1:540770032311:web:54b0d4ec1715779408cb32",
  measurementId: "G-5KTY505WKQ"
}

// Initialize Firebase
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0]

// Initialize Firebase Auth
export const auth = getAuth(app)

// Initialize Firestore
export const db = getFirestore(app)

// Initialize Realtime Database
export const realtimeDb = getDatabase(app)

// Initialize Analytics (only in browser)
export const analytics = typeof window !== 'undefined' ? getAnalytics(app) : null

export default app
