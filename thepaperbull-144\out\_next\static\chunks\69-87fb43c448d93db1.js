"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[69],{17069:(t,e,i)=>{i.d(e,{rM:()=>p,fx:()=>m});var s=i(95155),r=i(12115),a=i(81115),o=i(98915),n=i(50475),l=i(27759);class c{subscribe(t){return this.subscribers.push(t),t(this.state),()=>{let e=this.subscribers.indexOf(t);e>-1&&this.subscribers.splice(e,1)}}notifySubscribers(){this.subscribers.forEach(t=>t(this.state))}notifySubscribersDebounced(){this.updateTimeout&&clearTimeout(this.updateTimeout),this.updateTimeout=setTimeout(()=>{this.notifySubscribers()},100)}async initializeForUser(t){this.userId=t,this.state.isLoading=!0,this.notifySubscribers();try{await this.initializeAccountInfo(),this.setupRealtimeListeners(),this.state.isLoading=!1,this.state.error=null,this.notifySubscribers(),console.log("Realtime trading service initialized for user:",t)}catch(t){console.error("Error initializing realtime trading service:",t),this.state.isLoading=!1,this.state.error="Failed to initialize trading service",this.notifySubscribers()}}async initializeAccountInfo(){if(!this.userId)return;let t=n.A.getUserBalance();this.state.accountInfo={totalWalletBalance:t,totalUnrealizedProfit:0,totalMarginBalance:t,totalPositionInitialMargin:0,totalOpenOrderInitialMargin:0,availableBalance:t,maxWithdrawAmount:t,updateTime:Date.now()}}setupRealtimeListeners(){if(!this.userId)return;let t=(0,a.KR)(o.Ye,"users/".concat(this.userId,"/positions")),e=(0,a.Zy)(t,t=>{let e=t.val();this.state.positions=e?Object.entries(e).map(t=>{let[e,i]=t;return{id:e,...i,timestamp:i.timestamp||Date.now()}}):[],this.updateAccountInfo(),this.notifySubscribers()}),i=(0,a.KR)(o.Ye,"users/".concat(this.userId,"/orders")),s=(0,a.Zy)(i,t=>{let e=t.val();this.state.orders=e?Object.entries(e).map(t=>{let[e,i]=t;return{id:e,...i,timestamp:i.timestamp||Date.now()}}):[],this.updateAccountInfo(),this.notifySubscribers()}),r=(0,a.KR)(o.Ye,"users/".concat(this.userId,"/trades")),n=(0,a.Zy)(r,t=>{let e=t.val();this.state.trades=e?Object.entries(e).map(t=>{let[e,i]=t;return{id:e,...i,timestamp:i.timestamp||Date.now()}}).sort((t,e)=>e.timestamp-t.timestamp):[],this.notifySubscribers()});this.unsubscribeFunctions=[()=>(0,a.AU)(t,"value",e),()=>(0,a.AU)(i,"value",s),()=>(0,a.AU)(r,"value",n)]}updateAccountInfo(){if(!this.state.accountInfo)return;let t=n.A.getUserBalance(),e=this.state.positions.reduce((t,e)=>t+(e.pnl||0),0),i=this.state.positions.reduce((t,e)=>t+(e.margin||0),0),s=this.state.orders.filter(t=>"NEW"===t.status).reduce((t,e)=>{var i;let s=(null===(i=this.state.marketData[e.symbol])||void 0===i?void 0:i.price)||e.price;return t+e.origQty*s/(e.leverage||10)},0),r=i+s,a=Math.max(0,t-r);this.state.accountInfo.totalWalletBalance=t,this.state.accountInfo.totalUnrealizedProfit=e,this.state.accountInfo.totalPositionInitialMargin=i,this.state.accountInfo.totalOpenOrderInitialMargin=s,this.state.accountInfo.totalMarginBalance=t+e,this.state.accountInfo.availableBalance=a,this.state.accountInfo.maxWithdrawAmount=a,this.state.accountInfo.updateTime=Date.now(),console.log("Account Info Updated:",{userBalance:t,totalMargin:i,totalOrderMargin:s,totalUsedMargin:r,availableBalance:a,positionsCount:this.state.positions.length,ordersCount:this.state.orders.filter(t=>"NEW"===t.status).length})}updateMarketData(t,e){this.state.marketData[t]={...this.state.marketData[t],...e},e.price&&(this.state.positions=this.state.positions.map(i=>i.symbol===t?this.updatePositionPnL(i,e.price):i),this.updateAccountInfo(),this.notifySubscribersDebounced())}calculatePnL(t,e){let i=("LONG"===t.side?e-t.entryPrice:t.entryPrice-e)*t.size,s=t.margin>0?i/t.margin*100:0;return{pnl:Number(i.toFixed(2)),pnlPercent:Number(s.toFixed(2))}}updatePositionPnL(t,e){let{pnl:i,pnlPercent:s}=this.calculatePnL(t,e);return{...t,markPrice:e,pnl:i,pnlPercent:s}}calculateLiquidationPrice(t,e,i){let s=.995-1/i;return"LONG"===e?t*s:t*(2-s)}async placeOrder(t){var e;if(!this.userId){let t=n.A.getFirebaseUser(),e=n.A.getUser();if(console.log("Authentication check in placeOrder:",{realtimeServiceUserId:this.userId,firebaseUser:t?{uid:t.uid,email:t.email}:null,userServiceUser:e?{id:e.id,email:e.email}:null}),t)console.log("Using Firebase user ID as fallback:",t.uid),this.userId=t.uid,await this.initializeForUser(t.uid);else if(e&&e.id)console.log("Using user service ID as fallback:",e.id),this.userId=e.id,await this.initializeForUser(e.id);else throw console.error("Authentication failed - no user ID available"),Error("User not authenticated. Please sign in and try again.")}let i=Date.now();if(i-this.lastOrderTime<this.ORDER_COOLDOWN){let t=this.ORDER_COOLDOWN-(i-this.lastOrderTime);throw Error("Please wait ".concat(Math.ceil(t/1e3)," second(s) before placing another order"))}let s=(null===(e=this.state.marketData[t.symbol])||void 0===e?void 0:e.price)||t.price||0;if(s<=0)throw Error("Invalid market price. Please try again.");let r=t.quantity*s,c=r/(t.leverage||10),u=.001*r,d=n.A.getUserBalance(),h=this.state.positions.reduce((t,e)=>t+(e.margin||0),0),b=this.state.orders.filter(t=>"NEW"===t.status).reduce((t,e)=>{var i;let s=(null===(i=this.state.marketData[e.symbol])||void 0===i?void 0:i.price)||e.price;return t+e.origQty*s/(e.leverage||10)},0),p=h+b,m=d-p,y=c+u;if(console.log("Balance Validation:",{userBalance:d,currentMargin:h,pendingOrderMargin:b,totalUsedMargin:p,availableBalance:m,requiredMargin:c,commission:u,totalRequired:y,orderValue:r,leverage:t.leverage||10}),y>m)throw Error("Insufficient balance. Required: ".concat(y.toFixed(2)," USDT, Available: ").concat(m.toFixed(2)," USDT"));if(m<10)throw Error("Insufficient balance. Minimum 10 USDT required for trading.");let f="ord_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),g={id:f,symbol:t.symbol,side:t.side,type:t.type,origQty:t.quantity,executedQty:0,price:t.price||s,status:"NEW",timestamp:Date.now(),leverage:t.leverage||10};this.state.orders.push(g),this.updateAccountInfo(),this.notifySubscribers(),this.lastOrderTime=i;try{let e=(0,a.KR)(o.Ye,"users/".concat(this.userId,"/orders/").concat(f));return await (0,a.hZ)(e,{...g,createdAt:(0,a.O5)()}),"MARKET"===t.type&&await this.executeOrder(f,g,s),l.l.createTradeNotification(this.userId,"order_placed",{symbol:t.symbol,side:t.side,type:t.type,price:t.price||s,quantity:t.quantity}),f}catch(t){throw this.state.orders=this.state.orders.filter(t=>t.id!==f),this.updateAccountInfo(),this.notifySubscribers(),t}}async executeOrder(t,e,i){var s,r;if(!this.userId)return;let c=e.origQty*i*.001,u=e.origQty*i/e.leverage,d=(null===(r=n.A.getUser())||void 0===r?void 0:null===(s=r.balance)||void 0===s?void 0:s.current)||n.A.getUserBalance(),h=this.state.positions.reduce((t,e)=>t+(e.margin||0),0),b=d-h,p=u+c;if(console.log("Execution Balance Check:",{userBalance:d,currentMargin:h,availableBalance:b,margin:u,commission:c,totalRequired:p,orderId:t}),p>b)throw this.state.orders=this.state.orders.filter(e=>e.id!==t),this.updateAccountInfo(),this.notifySubscribers(),Error("Execution failed: Insufficient balance. Required: ".concat(p.toFixed(2)," USDT, Available: ").concat(b.toFixed(2)," USDT"));let m="pos_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),y={id:m,symbol:e.symbol,side:"BUY"===e.side?"LONG":"SHORT",entryPrice:i,markPrice:i,size:e.origQty,margin:u,leverage:e.leverage,pnl:0,pnlPercent:0,liquidationPrice:this.calculateLiquidationPrice(i,"BUY"===e.side?"LONG":"SHORT",e.leverage),timestamp:Date.now(),orderId:t};this.state.positions.push(y);let f=this.state.orders.findIndex(e=>e.id===t);-1!==f&&(this.state.orders[f].status="FILLED",this.state.orders[f].executedQty=e.origQty),this.updateAccountInfo(),this.notifySubscribers(),l.l.createTradeNotification(this.userId,"position_opened",{symbol:e.symbol,side:y.side,size:e.origQty,entryPrice:i});try{let e=(0,a.KR)(o.Ye,"users/".concat(this.userId,"/positions/").concat(m)),i=(0,a.KR)(o.Ye,"users/".concat(this.userId,"/orders/").concat(t));await Promise.all([(0,a.hZ)(e,{...y,createdAt:(0,a.O5)()}),(0,a.hZ)(i,{...this.state.orders[f],updatedAt:(0,a.O5)()})]);let s=n.A.getUserBalance();await n.A.updateBalance(s-c,"commission","Trading commission: ".concat(c.toFixed(2)," USDT"))}catch(t){console.error("Error saving position to Firebase:",t)}}async closePosition(t){var e;if(!this.userId)return;let i=this.state.positions.findIndex(e=>e.id===t);if(-1===i)return;let s=this.state.positions[i],r=(null===(e=this.state.marketData[s.symbol])||void 0===e?void 0:e.price)||s.markPrice,c=s.size*r*.001;this.state.positions.splice(i,1),this.updateAccountInfo(),this.notifySubscribers(),l.l.createTradeNotification(this.userId,"position_closed",{symbol:s.symbol,side:s.side,pnl:s.pnl,closePrice:r});try{let e="trade_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),i={id:e,symbol:s.symbol,side:"LONG"===s.side?"SELL":"BUY",price:r,quantity:s.size,commission:c,realizedPnl:s.pnl,timestamp:Date.now(),leverage:s.leverage,orderId:s.orderId||"",positionId:t};this.state.trades.unshift(i),this.notifySubscribers();let l=(0,a.KR)(o.Ye,"users/".concat(this.userId,"/positions/").concat(t)),u=(0,a.KR)(o.Ye,"users/".concat(this.userId,"/trades/").concat(e));await Promise.all([(0,a.TF)(l),(0,a.hZ)(u,{...i,createdAt:(0,a.O5)()})]);let d=n.A.getUserBalance();await n.A.updateBalance(d+s.pnl-c,s.pnl>0?"trade_profit":"trade_loss","Position closed: ".concat(s.pnl>0?"+":"").concat(s.pnl.toFixed(2)," USDT"))}catch(t){throw console.error("Error closing position in Firebase:",t),this.state.positions.push(s),this.updateAccountInfo(),this.notifySubscribers(),t}}async cancelOrder(t){if(!this.userId)throw Error("User not authenticated");try{await (0,a.TF)((0,a.KR)(o.Ye,"users/".concat(this.userId,"/orders/").concat(t))),this.state.orders=this.state.orders.filter(e=>e.id!==t),this.notifySubscribers(),console.log("Order canceled successfully:",t)}catch(t){throw console.error("Error canceling order:",t),t}}getState(){return{...this.state}}getMarketData(t){return this.state.marketData[t]||null}cleanup(){this.unsubscribeFunctions.forEach(t=>t()),this.unsubscribeFunctions=[],this.updateTimeout&&(clearTimeout(this.updateTimeout),this.updateTimeout=null),this.userId=null,this.state={positions:[],orders:[],trades:[],accountInfo:null,marketData:{},isLoading:!1,error:null},this.notifySubscribers()}constructor(){this.state={positions:[],orders:[],trades:[],accountInfo:null,marketData:{},isLoading:!1,error:null},this.subscribers=[],this.userId=null,this.unsubscribeFunctions=[],this.lastOrderTime=0,this.ORDER_COOLDOWN=1e3,this.updateTimeout=null,n.A.subscribe(t=>{console.log("Realtime Trading Service - User state changed:",{hasUser:!!t,userId:null==t?void 0:t.id,userStructure:t?Object.keys(t):null}),t&&t.id?this.userId!==t.id&&(console.log("Initializing realtime trading service for user:",t.id),this.initializeForUser(t.id)):(console.log("User logged out, cleaning up realtime trading service"),this.cleanup())});let t=n.A.getUser();t&&t.id&&!this.userId&&(console.log("Found existing user on startup, initializing:",t.id),this.initializeForUser(t.id))}}let u=new c;class d{initializeSimulators(){[{symbol:"BTCUSDT",basePrice:43200,volatility:.02},{symbol:"ETHUSDT",basePrice:2320,volatility:.025},{symbol:"SOLUSDT",basePrice:142,volatility:.03},{symbol:"ADAUSDT",basePrice:.45,volatility:.035},{symbol:"XRPUSDT",basePrice:.62,volatility:.04},{symbol:"BNBUSDT",basePrice:315,volatility:.025},{symbol:"DOGEUSDT",basePrice:.08,volatility:.05},{symbol:"TRXUSDT",basePrice:.12,volatility:.04},{symbol:"LINKUSDT",basePrice:14.5,volatility:.03},{symbol:"AVAXUSDT",basePrice:38.2,volatility:.035}].forEach(t=>{this.simulators.set(t.symbol,{symbol:t.symbol,basePrice:t.basePrice,volatility:t.volatility,trend:(Math.random()-.5)*.001,lastPrice:t.basePrice,lastUpdate:Date.now()})})}start(){this.intervalId||(this.intervalId=setInterval(()=>{this.updatePrices()},2e3))}stop(){this.intervalId&&(clearInterval(this.intervalId),this.intervalId=null)}updatePrices(){this.simulators.forEach(t=>{let e=Date.now(),i=(e-t.lastUpdate)/1e3,s=(Math.random()-.5)*t.volatility*i,r=t.trend*i,a=t.lastPrice*(1+(s+r)),o=a-t.lastPrice,n=o/t.lastPrice*100;.01>Math.random()&&(t.trend=(Math.random()-.5)*.001),t.lastPrice=a,t.lastUpdate=e;let l={symbol:t.symbol,price:a,priceChange:o,priceChangePercent:n,volume:1e6*Math.random(),timestamp:e};this.notifySubscribers(t.symbol,l)})}subscribe(t){return this.subscribers.push(t),()=>{let e=this.subscribers.indexOf(t);e>-1&&this.subscribers.splice(e,1)}}notifySubscribers(t,e){this.subscribers.forEach(i=>i(t,e))}getCurrentPrice(t){let e=this.simulators.get(t);return e?e.lastPrice:null}addSymbol(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.03;this.simulators.has(t)||this.simulators.set(t,{symbol:t,basePrice:e,volatility:i,trend:(Math.random()-.5)*.001,lastPrice:e,lastUpdate:Date.now()})}removeSymbol(t){this.simulators.delete(t)}constructor(){this.simulators=new Map,this.intervalId=null,this.subscribers=[],this.initializeSimulators()}}let h=new d,b=(0,r.createContext)(void 0);function p(t){let{children:e}=t,[i,a]=(0,r.useState)({positions:[],orders:[],trades:[],marketData:{},accountInfo:null,isLoading:!1,error:null});(0,r.useEffect)(()=>{let t=u.subscribe(t=>{console.log("Trading context received state update:",{positions:t.positions.length,orders:t.orders.length,trades:t.trades.length,isLoading:t.isLoading,error:t.error}),a(t)}),e=u.getState();return e&&(console.log("Trading context initial state sync:",{positions:e.positions.length,orders:e.orders.length,trades:e.trades.length}),a(e)),t},[]),(0,r.useEffect)(()=>{let t=h.subscribe((t,e)=>{u.updateMarketData(t,e)});return h.start(),()=>{t(),h.stop()}},[]);let o=(0,r.useCallback)(async t=>{try{return await u.placeOrder(t)}catch(t){throw console.error("Failed to place order:",t),t}},[]),n=(0,r.useCallback)(async t=>{try{return console.log("Cancel order not implemented yet:",t),!0}catch(t){throw console.error("Failed to cancel order:",t),t}},[]),l=(0,r.useCallback)(async t=>{try{return await u.closePosition(t),!0}catch(t){throw console.error("Failed to close position:",t),t}},[]),c=(0,r.useCallback)(async t=>{try{return console.log("Position update not implemented in Firebase service yet:",t),!0}catch(t){throw console.error("Failed to update position:",t),t}},[]),d=(0,r.useCallback)((t,e)=>{u.updateMarketData(t,e)},[]),p=(0,r.useCallback)(t=>{console.log("Account info update not needed with Realtime service:",t)},[]),m=(0,r.useCallback)(()=>{a(t=>({...t,error:null}))},[]),y=(0,r.useCallback)(t=>u.getMarketData(t),[]),f=(0,r.useCallback)(t=>i.positions.find(e=>e.symbol===t)||null,[i.positions]),g=(0,r.useCallback)(()=>i.accountInfo,[i.accountInfo]),v=(0,r.useCallback)(()=>i.positions.reduce((t,e)=>t+e.pnl,0),[i.positions]),I=(0,r.useCallback)(()=>i.positions.reduce((t,e)=>t+e.margin,0),[i.positions]),D=(0,r.useCallback)(()=>{var t;return(null===(t=i.accountInfo)||void 0===t?void 0:t.availableBalance)||0},[i.accountInfo]),P={positions:i.positions,orders:i.orders,trades:i.trades,marketData:i.marketData,accountInfo:i.accountInfo,isLoading:i.isLoading,error:i.error,state:i,placeOrder:o,cancelOrder:n,closePosition:l,updatePosition:c,updateMarketData:d,updateAccountInfo:p,clearError:m,getMarketData:y,getPositionBySymbol:f,getAccountInfo:g,getTotalPnL:v,getTotalMargin:I,getAvailableBalance:D};return(0,s.jsx)(b.Provider,{value:P,children:e})}function m(){let t=(0,r.useContext)(b);if(void 0===t)throw Error("useTrading must be used within a TradingProvider");return t}}}]);