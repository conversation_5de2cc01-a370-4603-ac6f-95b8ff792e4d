"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./app/login/page.tsx":
/*!****************************!*\
  !*** ./app/login/page.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./contexts/auth-context.tsx\");\n/* harmony import */ var _lib_auth_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth-utils */ \"(app-pages-browser)/./lib/auth-utils.ts\");\n/* harmony import */ var _components_ui_notification__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/notification */ \"(app-pages-browser)/./components/ui/notification.tsx\");\n/* harmony import */ var _lib_user_preferences_service__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/user-preferences-service */ \"(app-pages-browser)/./lib/user-preferences-service.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-pie.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction LoginPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { signIn, signInWithGoogle } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const notifications = (0,_components_ui_notification__WEBPACK_IMPORTED_MODULE_5__.useNotifications)();\n    // Authentication states\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [rememberMe, setRememberMe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Modal states\n    const [showSignInModal, setShowSignInModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSignUpModal, setShowSignUpModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Sign up form states\n    const [signUpData, setSignUpData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        firstName: \"\",\n        lastName: \"\",\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\"\n    });\n    // Remember me functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPage.useEffect\": ()=>{\n            const rememberedUsername = _lib_user_preferences_service__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getRememberedUsername();\n            if (rememberedUsername) {\n                setUsername(rememberedUsername);\n                setRememberMe(true);\n            }\n        }\n    }[\"LoginPage.useEffect\"], []);\n    // Check for registration success message\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPage.useEffect\": ()=>{\n            const registered = searchParams.get(\"registered\");\n            const registeredEmail = sessionStorage.getItem(\"registeredEmail\");\n            if (registered === \"true\" && registeredEmail) {\n                setSuccessMessage(\"Account created successfully! You can now log in with \".concat(registeredEmail));\n                setUsername(registeredEmail);\n                // Clear the stored email after displaying the message\n                sessionStorage.removeItem(\"registeredEmail\");\n            }\n        }\n    }[\"LoginPage.useEffect\"], [\n        searchParams\n    ]);\n    // Real Firebase authentication login\n    const handleLogin = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        setIsLoading(true);\n        try {\n            await signIn(username, password);\n            // Store username if remember me is checked\n            if (rememberMe) {\n                _lib_user_preferences_service__WEBPACK_IMPORTED_MODULE_6__[\"default\"].updateRememberedUsername(username);\n            } else {\n                _lib_user_preferences_service__WEBPACK_IMPORTED_MODULE_6__[\"default\"].updateRememberedUsername(null);\n            }\n            // Show success notification\n            notifications.success(\"Successfully signed in!\", {\n                title: \"Welcome back!\",\n                duration: 3000\n            });\n            // Redirect to trade page\n            router.push(\"/trade\");\n        } catch (error) {\n            console.error('Login error:', error);\n            const errorMessage = error.message || 'An error occurred during login';\n            notifications.error(errorMessage, {\n                title: \"Sign In Failed\",\n                duration: 5000\n            });\n            setError(errorMessage);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Real Google authentication handler\n    const handleGoogleAuth = async ()=>{\n        setIsLoading(true);\n        setError(\"\");\n        try {\n            await signInWithGoogle();\n            // Show success notification\n            notifications.success(\"Successfully signed in with Google!\", {\n                title: \"Welcome back!\",\n                duration: 3000\n            });\n            router.push(\"/trade\");\n        } catch (error) {\n            console.error('Google auth error:', error);\n            // Handle specific Firebase auth errors\n            if (error.code === 'auth/popup-closed-by-user') {\n            // User closed the popup - don't show an error, just reset loading state\n            // No notification needed as this is user-initiated\n            } else if (error.code === 'auth/popup-blocked') {\n                notifications.error('Popup was blocked by your browser. Please allow popups and try again.', {\n                    title: \"Popup Blocked\",\n                    duration: 5000\n                });\n                setError('Popup was blocked by your browser. Please allow popups and try again.');\n            } else if (error.code === 'auth/cancelled-popup-request') {\n            // User cancelled - no notification needed\n            } else {\n                notifications.error(error.message || 'Failed to sign in with Google', {\n                    title: \"Sign In Failed\",\n                    duration: 5000\n                });\n                setError(error.message || 'Failed to sign in with Google');\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Real Firebase sign up handler\n    const handleSignUp = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        if (signUpData.password !== signUpData.confirmPassword) {\n            setError(\"Passwords don't match\");\n            return;\n        }\n        setIsLoading(true);\n        try {\n            const displayName = \"\".concat(signUpData.firstName, \" \").concat(signUpData.lastName).trim();\n            await (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_4__.signUpWithEmail)(signUpData.email, signUpData.password, displayName);\n            // Show success notification\n            notifications.success(\"Account created successfully! Please sign in.\", {\n                title: \"Welcome to ThePaperBull!\",\n                duration: 4000\n            });\n            setShowSignUpModal(false);\n            setShowSignInModal(true);\n            setUsername(signUpData.email);\n            setSuccessMessage(\"Account created successfully! Please sign in.\");\n            // Reset sign up form\n            setSignUpData({\n                firstName: \"\",\n                lastName: \"\",\n                email: \"\",\n                password: \"\",\n                confirmPassword: \"\"\n            });\n        } catch (error) {\n            console.error('Sign up error:', error);\n            const errorMessage = error.message || 'Failed to create account';\n            notifications.error(errorMessage, {\n                title: \"Account Creation Failed\",\n                duration: 5000\n            });\n            setError(errorMessage);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-emerald-50 dark:from-slate-900 dark:via-slate-800 dark:to-emerald-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"relative z-50 bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl border-b border-slate-200/50 dark:border-slate-700/50 shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center py-4 sm:py-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-br from-emerald-500 via-blue-500 to-purple-600 rounded-2xl p-2 sm:p-3 shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-5 w-5 sm:h-7 sm:w-7 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-br from-emerald-400 via-blue-400 to-purple-500 rounded-2xl blur opacity-30 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3 sm:ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-xl sm:text-3xl font-black bg-gradient-to-r from-emerald-600 via-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                                    children: \"ThePaperBull\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs sm:text-sm text-slate-500 dark:text-slate-400 font-medium hidden sm:block\",\n                                                    children: \"AI-Powered Trading Platform\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                        className: \"text-slate-700 dark:text-slate-200 p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowSignInModal(true),\n                                            className: \"text-slate-700 dark:text-slate-200 hover:text-emerald-600 dark:hover:text-emerald-400 font-semibold px-4 py-2 rounded-xl hover:bg-slate-100 dark:hover:bg-slate-800 transition-all duration-200\",\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowSignUpModal(true),\n                                            className: \"bg-gradient-to-r from-emerald-600 via-blue-600 to-purple-600 text-white px-8 py-3 rounded-xl font-semibold hover:from-emerald-700 hover:via-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                                            children: \"Get Started\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this),\n                        mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden absolute top-full left-0 right-0 bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl border-b border-slate-200/50 dark:border-slate-700/50 shadow-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-6 space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowSignInModal(true);\n                                            setMobileMenuOpen(false);\n                                        },\n                                        className: \"w-full text-left text-slate-700 dark:text-slate-200 hover:text-emerald-600 dark:hover:text-emerald-400 font-semibold px-4 py-3 rounded-xl hover:bg-slate-100 dark:hover:bg-slate-800 transition-all duration-200\",\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowSignUpModal(true);\n                                            setMobileMenuOpen(false);\n                                        },\n                                        className: \"w-full bg-gradient-to-r from-emerald-600 via-blue-600 to-purple-600 text-white px-8 py-3 rounded-xl font-semibold hover:from-emerald-700 hover:via-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg\",\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative overflow-hidden pt-4 pb-12 lg:pb-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-12 gap-6 lg:gap-8 items-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-7 text-center lg:text-left order-2 lg:order-1 pt-4 lg:pt-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center bg-gradient-to-r from-emerald-50 to-blue-50 dark:from-emerald-900/30 dark:to-blue-900/30 text-emerald-700 dark:text-emerald-300 px-4 py-2 rounded-full text-sm font-semibold mb-6 border border-emerald-200/50 dark:border-emerald-700/50 shadow-sm backdrop-blur-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-emerald-500 rounded-full mr-2 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:inline\",\n                                                    children: \"\\uD83D\\uDE80 Join 10,000+ Professional Traders\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sm:hidden\",\n                                                    children: \"\\uD83D\\uDE80 Join 10K+ Traders\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 ml-2 text-yellow-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl sm:text-5xl lg:text-6xl font-black text-slate-900 dark:text-white mb-6 leading-[1.1] tracking-tight\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"block mb-2\",\n                                                    children: \"Master Crypto\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"block bg-gradient-to-r from-emerald-600 via-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                                    children: \"Futures Trading\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg sm:text-xl text-slate-600 dark:text-slate-300 mb-8 leading-relaxed max-w-2xl\",\n                                            children: [\n                                                \"Professional paper trading with\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold text-emerald-600 dark:text-emerald-400\",\n                                                    children: \" real Binance data\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \",\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold text-blue-600 dark:text-blue-400\",\n                                                    children: \" TradingView charts\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \", and\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold text-purple-600 dark:text-purple-400\",\n                                                    children: \" advanced tools\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \".\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 sm:grid-cols-3 gap-4 mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-lg p-3 border border-slate-200/50 dark:border-slate-700/50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-4 w-4 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-bold text-slate-900 dark:text-white\",\n                                                                    children: \"$10,000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-slate-500 dark:text-slate-400\",\n                                                                    children: \"Virtual Capital\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-lg p-3 border border-slate-200/50 dark:border-slate-700/50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-bold text-slate-900 dark:text-white\",\n                                                                    children: \"Real-Time\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-slate-500 dark:text-slate-400\",\n                                                                    children: \"Market Data\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-lg p-3 border border-slate-200/50 dark:border-slate-700/50 col-span-2 sm:col-span-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-bold text-slate-900 dark:text-white\",\n                                                                    children: \"125x\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 356,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-slate-500 dark:text-slate-400\",\n                                                                    children: \"Max Leverage\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 357,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowSignUpModal(true),\n                                                    className: \"group relative bg-gradient-to-r from-emerald-600 to-blue-600 text-white px-8 py-4 rounded-xl font-bold text-lg hover:from-emerald-700 hover:to-blue-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1 hover:scale-105 overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"relative z-10 flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"mr-3 h-5 w-5 group-hover:animate-bounce\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Start Trading Free\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"inline-block ml-3 h-5 w-5 group-hover:translate-x-1 transition-transform\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-r from-emerald-400 to-blue-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowSignInModal(true),\n                                                    className: \"group flex items-center justify-center bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm text-slate-700 dark:text-slate-200 px-8 py-4 rounded-xl font-semibold text-lg border border-slate-200 dark:border-slate-600 hover:bg-white dark:hover:bg-slate-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"mr-3 h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Sign In\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap items-center justify-center lg:justify-start gap-4 text-sm text-slate-500 dark:text-slate-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-4 w-4 text-emerald-500 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"No Credit Card Required\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4 text-blue-500 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Bank-Grade Security\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-5 order-1 lg:order-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/95 dark:bg-slate-800/95 backdrop-blur-xl rounded-2xl p-6 shadow-2xl border border-slate-200/50 dark:border-slate-700/50 max-w-md mx-auto lg:max-w-none sticky top-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-emerald-600 to-blue-600 rounded-xl mb-3 shadow-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-6 w-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-bold text-slate-900 dark:text-white mb-1\",\n                                                        children: \"Join ThePaperBull\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-slate-600 dark:text-slate-300\",\n                                                        children: \"Professional crypto futures trading\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-3 gap-3 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-3 bg-emerald-50 dark:bg-emerald-900/20 rounded-lg border border-emerald-200/50 dark:border-emerald-700/50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-emerald-600 dark:text-emerald-400\",\n                                                                children: \"$10K\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-emerald-600/70 dark:text-emerald-400/70\",\n                                                                children: \"Capital\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200/50 dark:border-blue-700/50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-blue-600 dark:text-blue-400\",\n                                                                children: \"125x\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-blue-600/70 dark:text-blue-400/70\",\n                                                                children: \"Leverage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200/50 dark:border-purple-700/50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-purple-600 dark:text-purple-400\",\n                                                                children: \"Free\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-purple-600/70 dark:text-purple-400/70\",\n                                                                children: \"Forever\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleGoogleAuth,\n                                                        disabled: isLoading,\n                                                        className: \"w-full flex items-center justify-center bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-200 px-6 py-3.5 rounded-xl font-semibold border border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-600 transition-all duration-200 shadow-md hover:shadow-lg group\",\n                                                        children: [\n                                                            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-slate-700 dark:border-slate-200 mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"h-5 w-5 mr-3 group-hover:scale-110 transition-transform\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fill: \"#4285F4\",\n                                                                        d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 437,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fill: \"#34A853\",\n                                                                        d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 438,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fill: \"#FBBC05\",\n                                                                        d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 439,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fill: \"#EA4335\",\n                                                                        d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 440,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Continue with Google\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 flex items-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full border-t border-slate-300 dark:border-slate-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 448,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative flex justify-center text-sm\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-3 bg-white/95 dark:bg-slate-800/95 text-slate-500\",\n                                                                    children: \"Or\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 451,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowSignInModal(true),\n                                                                className: \"flex items-center justify-center bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-200 px-4 py-3 rounded-xl font-medium hover:bg-slate-200 dark:hover:bg-slate-600 transition-all duration-200 group\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4 group-hover:scale-110 transition-transform\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 460,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Sign In\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowSignUpModal(true),\n                                                                className: \"flex items-center justify-center bg-gradient-to-r from-emerald-600 to-blue-600 text-white px-4 py-3 rounded-xl font-medium hover:from-emerald-700 hover:to-blue-700 transition-all duration-200 group\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4 group-hover:scale-110 transition-transform\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 467,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Sign Up\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6 pt-4 border-t border-slate-200/50 dark:border-slate-700/50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center text-xs text-slate-500 dark:text-slate-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-3 w-3 text-emerald-500 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 476,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Secured by Firebase • 10,000+ Traders\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-20 left-10 w-72 h-72 bg-emerald-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 487,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-40 right-10 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -bottom-8 left-20 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 486,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 305,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-20 bg-white dark:bg-slate-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12 sm:mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl sm:text-4xl font-bold text-slate-900 dark:text-white mb-3 sm:mb-4 px-4\",\n                                    children: \"Everything You Need to Master Trading\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-base sm:text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto px-4\",\n                                    children: \"Professional-grade tools and insights to help you become a better trader\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8\",\n                            children: [\n                                {\n                                    icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                                    title: \"Professional Charting\",\n                                    description: \"TradingView integration with 100+ technical indicators, drawing tools, and multi-timeframe analysis\",\n                                    gradient: \"from-emerald-600 to-emerald-700\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                    title: \"Real-Time Market Data\",\n                                    description: \"Live cryptocurrency prices from Binance with WebSocket connections for instant updates\",\n                                    gradient: \"from-blue-600 to-blue-700\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                    title: \"Futures Trading Simulation\",\n                                    description: \"Practice crypto futures with leverage up to 125x using real market conditions and $10K virtual capital\",\n                                    gradient: \"from-purple-600 to-purple-700\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                    title: \"Advanced Risk Management\",\n                                    description: \"Stop-loss, take-profit orders, position sizing calculator, and portfolio risk analytics\",\n                                    gradient: \"from-orange-600 to-orange-700\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                                    title: \"Performance Analytics\",\n                                    description: \"Detailed P&L tracking, win rate analysis, and comprehensive trading statistics dashboard\",\n                                    gradient: \"from-pink-600 to-pink-700\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                    title: \"Instant Order Execution\",\n                                    description: \"Lightning-fast order placement with market and limit orders, plus advanced order types\",\n                                    gradient: \"from-cyan-600 to-cyan-700\"\n                                }\n                            ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group bg-white dark:bg-slate-800 rounded-2xl p-6 sm:p-8 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r \".concat(feature.gradient, \" rounded-xl p-3 w-fit mb-4 sm:mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg sm:text-xl font-bold text-slate-900 dark:text-white mb-3 group-hover:text-emerald-600 dark:group-hover:text-emerald-400 transition-colors\",\n                                            children: feature.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm sm:text-base text-slate-600 dark:text-slate-300 leading-relaxed\",\n                                            children: feature.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 544,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 505,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 495,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 494,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-20 bg-gradient-to-r from-emerald-600 via-blue-600 to-purple-600 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-xl animate-float\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 564,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-10 right-10 w-24 h-24 bg-white rounded-full blur-xl animate-float animation-delay-2000\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 565,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-white rounded-full blur-2xl animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 566,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 563,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-8 sm:mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl sm:text-4xl font-bold text-white mb-3 sm:mb-4\",\n                                        children: \"Trusted by Crypto Traders Worldwide\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 571,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-base sm:text-xl text-emerald-100 max-w-2xl mx-auto\",\n                                        children: \"Join thousands of successful traders who've mastered crypto futures with ThePaperBull\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 570,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8 text-center\",\n                                children: [\n                                    {\n                                        number: \"10,000+\",\n                                        label: \"Active Traders\",\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"]\n                                    },\n                                    {\n                                        number: \"$50M+\",\n                                        label: \"Virtual Volume Traded\",\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                                    },\n                                    {\n                                        number: \"99.9%\",\n                                        label: \"Platform Uptime\",\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                    },\n                                    {\n                                        number: \"24/7\",\n                                        label: \"Market Access\",\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"]\n                                    }\n                                ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/10 backdrop-blur-sm rounded-2xl p-4 sm:p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 hover:scale-105\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center mb-2 sm:mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                        className: \"h-6 w-6 sm:h-8 sm:w-8 text-white group-hover:scale-110 transition-transform\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 588,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl sm:text-4xl font-bold text-white mb-1 sm:mb-2 group-hover:text-emerald-200 transition-colors\",\n                                                    children: stat.number\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 591,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs sm:text-base text-emerald-100 font-medium\",\n                                                    children: stat.label\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 579,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 569,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 561,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-20 bg-slate-50 dark:bg-slate-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12 sm:mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl sm:text-4xl font-bold text-slate-900 dark:text-white mb-3 sm:mb-4\",\n                                    children: \"What Traders Are Saying\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-base sm:text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto\",\n                                    children: \"Real feedback from traders who've improved their skills with ThePaperBull\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 611,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 607,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8\",\n                            children: [\n                                {\n                                    name: \"Alex Chen\",\n                                    role: \"Crypto Day Trader\",\n                                    content: \"ThePaperBull helped me master futures trading without risking real money. The TradingView integration and real-time data are incredible. I'm now profitable in live trading!\",\n                                    rating: 5,\n                                    avatar: \"AC\"\n                                },\n                                {\n                                    name: \"Sarah Martinez\",\n                                    role: \"Portfolio Manager\",\n                                    content: \"The risk management tools and analytics dashboard are professional-grade. I use it to test new strategies before implementing them with client funds. Absolutely essential.\",\n                                    rating: 5,\n                                    avatar: \"SM\"\n                                },\n                                {\n                                    name: \"Michael Thompson\",\n                                    role: \"Trading Educator\",\n                                    content: \"I recommend ThePaperBull to all my students. The platform's accuracy with Binance data and comprehensive features make it the best paper trading platform available.\",\n                                    rating: 5,\n                                    avatar: \"MT\"\n                                }\n                            ].map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-slate-800 rounded-2xl p-6 sm:p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-200 dark:border-slate-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-r from-emerald-600 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4\",\n                                                    children: testimonial.avatar\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 642,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-bold text-slate-900 dark:text-white\",\n                                                            children: testimonial.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 646,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                                            children: testimonial.role\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 647,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 645,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex mb-4\",\n                                            children: [\n                                                ...Array(testimonial.rating)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-5 w-5 text-yellow-400 fill-current\"\n                                                }, i, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-6 w-6 text-emerald-600 mb-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 657,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600 dark:text-slate-300 leading-relaxed italic\",\n                                            children: [\n                                                '\"',\n                                                testimonial.content,\n                                                '\"'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 658,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 616,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 606,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 605,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-20 bg-gradient-to-br from-slate-900 via-emerald-900 to-blue-900 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-emerald-500/20 to-blue-500/20\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 671,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-20 left-20 w-96 h-96 bg-emerald-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 672,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-20 right-20 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse animation-delay-2000\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 673,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 670,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-6xl mx-auto text-center px-4 sm:px-6 lg:px-8 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center bg-gradient-to-r from-emerald-500/20 to-blue-500/20 text-emerald-300 px-3 sm:px-6 py-2 sm:py-3 rounded-full text-xs sm:text-sm font-semibold mb-6 sm:mb-8 border border-emerald-500/30 animate-pulse-glow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 678,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:inline\",\n                                        children: \"\\uD83D\\uDE80 Start with $10,000 Virtual Capital - No Credit Card Required\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sm:hidden\",\n                                        children: \"\\uD83D\\uDE80 $10K Virtual Capital\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 680,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 677,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl sm:text-5xl md:text-7xl font-black text-white mb-6 sm:mb-8 leading-tight px-2\",\n                                children: [\n                                    \"Ready to Master\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block bg-gradient-to-r from-emerald-400 via-blue-400 to-purple-400 bg-clip-text text-transparent animate-gradient\",\n                                        children: \"Crypto Futures Trading?\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 685,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 683,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-base sm:text-xl text-slate-300 mb-8 sm:mb-12 max-w-3xl mx-auto leading-relaxed px-4\",\n                                children: [\n                                    \"Join 10,000+ traders who've mastered crypto futures with our professional-grade paper trading platform.\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:inline\",\n                                        children: \" Practice with real Binance data, TradingView charts, and advanced risk management tools.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 690,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 mb-8 sm:mb-12 max-w-4xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center bg-white/10 backdrop-blur-sm rounded-xl p-3 sm:p-4 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 sm:h-5 sm:w-5 text-emerald-400 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 698,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm sm:text-base font-medium\",\n                                                children: \"Real Market Data\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 697,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center bg-white/10 backdrop-blur-sm rounded-xl p-3 sm:p-4 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 sm:h-5 sm:w-5 text-emerald-400 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 702,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm sm:text-base font-medium\",\n                                                children: \"125x Leverage Simulation\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 703,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center bg-white/10 backdrop-blur-sm rounded-xl p-3 sm:p-4 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 sm:h-5 sm:w-5 text-emerald-400 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 706,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm sm:text-base font-medium\",\n                                                children: \"Advanced Analytics\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 707,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 705,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 sm:gap-6 justify-center items-center px-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowSignUpModal(true),\n                                        className: \"group relative bg-gradient-to-r from-emerald-500 to-blue-500 text-white px-8 sm:px-12 py-4 sm:py-5 rounded-2xl font-bold text-lg sm:text-xl hover:from-emerald-600 hover:to-blue-600 transition-all duration-300 shadow-2xl hover:shadow-3xl transform hover:-translate-y-2 hover:scale-105 w-full sm:w-auto overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10 flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6 group-hover:animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Start Trading Free - $10K Capital\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"inline-block ml-2 sm:ml-3 h-5 w-5 sm:h-6 sm:w-6 group-hover:translate-x-1 transition-transform\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 719,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-emerald-400 to-blue-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 721,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300 animate-shimmer\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 722,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 712,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-3 sm:gap-4 w-full sm:w-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowSignInModal(true),\n                                                className: \"group bg-white/10 backdrop-blur-sm text-white px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold text-base sm:text-lg border-2 border-white/20 hover:bg-white/20 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1 flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4 sm:h-5 sm:w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Sign In\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 726,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleGoogleAuth,\n                                                disabled: isLoading,\n                                                className: \"group bg-white text-slate-900 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold text-base sm:text-lg border-2 border-white/20 hover:bg-slate-100 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1 flex items-center justify-center\",\n                                                children: [\n                                                    isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-4 w-4 sm:h-5 sm:w-5 border-b-2 border-slate-900 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 740,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"h-4 w-4 sm:h-5 sm:w-5 mr-2\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fill: \"#4285F4\",\n                                                                d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 743,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fill: \"#34A853\",\n                                                                d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 744,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fill: \"#FBBC05\",\n                                                                d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 745,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fill: \"#EA4335\",\n                                                                d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 746,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 742,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Google\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 734,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 725,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 711,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 676,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 668,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-slate-900 text-white py-8 sm:py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-emerald-500 via-blue-500 to-purple-600 rounded-xl p-2 mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5 sm:h-6 sm:w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 763,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 762,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg sm:text-xl font-bold bg-gradient-to-r from-emerald-400 via-blue-400 to-purple-400 bg-clip-text text-transparent\",\n                                                children: \"ThePaperBull\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-400 text-xs sm:text-sm\",\n                                                children: \"AI-Powered Trading Platform\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 769,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 761,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    {\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n                                        href: \"#\"\n                                    },\n                                    {\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n                                        href: \"#\"\n                                    },\n                                    {\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n                                        href: \"#\"\n                                    },\n                                    {\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n                                        href: \"#\"\n                                    }\n                                ].map((social, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: social.href,\n                                        className: \"w-8 h-8 sm:w-10 sm:h-10 bg-slate-800 rounded-xl flex items-center justify-center hover:bg-gradient-to-r hover:from-emerald-600 hover:to-blue-600 transition-all duration-300 transform hover:scale-110\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(social.icon, {\n                                            className: \"h-4 w-4 sm:h-5 sm:w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 786,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 781,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 774,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-slate-800 pt-6 w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-400 text-xs sm:text-sm\",\n                                        children: \"\\xa9 2024 ThePaperBull. All rights reserved.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 792,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row justify-center items-center gap-2 sm:gap-4 mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-emerald-400 transition-colors text-xs sm:text-sm\",\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 796,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline text-slate-600\",\n                                                children: \"|\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 797,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-emerald-400 transition-colors text-xs sm:text-sm\",\n                                                children: \"Terms of Service\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 798,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 795,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 791,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 759,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 758,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 757,\n                columnNumber: 7\n            }, this),\n            showSignInModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-slate-800 rounded-2xl p-6 sm:p-8 w-full max-w-md relative max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowSignInModal(false),\n                            className: \"absolute top-4 right-4 text-slate-400 hover:text-slate-600 dark:hover:text-slate-200 z-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                className: \"h-5 w-5 sm:h-6 sm:w-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 813,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 809,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-6 sm:mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-emerald-600 to-blue-600 rounded-xl p-2 sm:p-3 w-fit mx-auto mb-3 sm:mb-4 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-6 w-6 sm:h-8 sm:w-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 818,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 817,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl sm:text-2xl font-bold text-slate-900 dark:text-white\",\n                                    children: \"Welcome Back, Trader\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 820,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm sm:text-base text-slate-600 dark:text-slate-300\",\n                                    children: \"Continue your trading journey with ThePaperBull\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 821,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 816,\n                            columnNumber: 13\n                        }, this),\n                        successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-emerald-50 dark:bg-emerald-900/20 border border-emerald-200 dark:border-emerald-800 text-emerald-800 dark:text-emerald-300 p-4 rounded-lg mb-6 text-sm\",\n                            children: successMessage\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 825,\n                            columnNumber: 15\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 p-4 rounded-lg mb-6 text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 831,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleLogin,\n                            className: \"space-y-4 sm:space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-xs sm:text-sm font-medium text-slate-700 dark:text-slate-300 mb-1 sm:mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 838,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            value: username,\n                                            onChange: (e)=>setUsername(e.target.value),\n                                            className: \"w-full px-3 sm:px-4 py-2 sm:py-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-slate-700 dark:text-white text-sm sm:text-base\",\n                                            placeholder: \"Enter your email\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 841,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 837,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-xs sm:text-sm font-medium text-slate-700 dark:text-slate-300 mb-1 sm:mb-2\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 852,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    value: password,\n                                                    onChange: (e)=>setPassword(e.target.value),\n                                                    className: \"w-full px-3 sm:px-4 py-2 sm:py-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-slate-700 dark:text-white pr-10 sm:pr-12 text-sm sm:text-base\",\n                                                    placeholder: \"Enter your password\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 856,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                    className: \"absolute right-2 sm:right-3 top-2 sm:top-3 text-slate-400 hover:text-slate-600 dark:hover:text-slate-200\",\n                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                        className: \"h-4 w-4 sm:h-5 sm:w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 869,\n                                                        columnNumber: 37\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                        className: \"h-4 w-4 sm:h-5 sm:w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 869,\n                                                        columnNumber: 84\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 864,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 855,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 851,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    checked: rememberMe,\n                                                    onChange: (e)=>setRememberMe(e.target.checked),\n                                                    className: \"rounded border-slate-300 text-emerald-600 focus:ring-emerald-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 876,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 text-xs sm:text-sm text-slate-600 dark:text-slate-300\",\n                                                    children: \"Remember me\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 882,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 875,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"text-xs sm:text-sm text-emerald-600 hover:text-emerald-500 text-left sm:text-right\",\n                                            children: \"Forgot password?\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 884,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 874,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading,\n                                    className: \"w-full bg-gradient-to-r from-emerald-600 to-blue-600 text-white py-2.5 sm:py-3 rounded-lg font-semibold hover:from-emerald-700 hover:to-blue-700 transition-all duration-200 disabled:opacity-50 text-sm sm:text-base\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 sm:h-5 sm:w-5 border-b-2 border-white mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 896,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Signing in...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 895,\n                                        columnNumber: 19\n                                    }, this) : \"Sign In\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 889,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 836,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 sm:mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full border-t border-slate-300 dark:border-slate-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 908,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 907,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex justify-center text-xs sm:text-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 bg-white dark:bg-slate-800 text-slate-500\",\n                                                children: \"Or continue with\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 911,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 910,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 906,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleGoogleAuth,\n                                    disabled: isLoading,\n                                    className: \"w-full mt-3 sm:mt-4 flex items-center justify-center bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-200 py-2.5 sm:py-3 rounded-lg font-semibold border border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-600 transition-all duration-200 text-sm sm:text-base\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-4 w-4 sm:h-5 sm:w-5 mr-2 sm:mr-3\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#4285F4\",\n                                                    d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 921,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#34A853\",\n                                                    d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 922,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#FBBC05\",\n                                                    d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 923,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#EA4335\",\n                                                    d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 924,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 920,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Continue with Google\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 915,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 905,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 sm:mt-6 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs sm:text-sm text-slate-600 dark:text-slate-300\",\n                                children: [\n                                    \"Don't have an account?\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowSignInModal(false);\n                                            setShowSignUpModal(true);\n                                        },\n                                        className: \"text-emerald-600 hover:text-emerald-500 font-medium\",\n                                        children: \"Sign up\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 933,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 931,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 930,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 808,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 807,\n                columnNumber: 9\n            }, this),\n            showSignUpModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-slate-800 rounded-2xl p-6 sm:p-8 w-full max-w-md relative max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowSignUpModal(false),\n                            className: \"absolute top-4 right-4 text-slate-400 hover:text-slate-600 dark:hover:text-slate-200 z-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                className: \"h-5 w-5 sm:h-6 sm:w-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 958,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 954,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-6 sm:mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-emerald-600 to-blue-600 rounded-xl p-2 sm:p-3 w-fit mx-auto mb-3 sm:mb-4 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-6 w-6 sm:h-8 sm:w-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 963,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 962,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl sm:text-2xl font-bold text-slate-900 dark:text-white\",\n                                    children: \"Join ThePaperBull\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 965,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm sm:text-base text-slate-600 dark:text-slate-300\",\n                                    children: \"Start mastering crypto futures with $10,000 virtual capital\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 966,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 961,\n                            columnNumber: 13\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 p-4 rounded-lg mb-6 text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 970,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSignUp,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                    children: \"First Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 978,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: signUpData.firstName,\n                                                    onChange: (e)=>setSignUpData({\n                                                            ...signUpData,\n                                                            firstName: e.target.value\n                                                        }),\n                                                    className: \"w-full px-4 py-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-slate-700 dark:text-white\",\n                                                    placeholder: \"John\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 981,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 977,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                    children: \"Last Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 991,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: signUpData.lastName,\n                                                    onChange: (e)=>setSignUpData({\n                                                            ...signUpData,\n                                                            lastName: e.target.value\n                                                        }),\n                                                    className: \"w-full px-4 py-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-slate-700 dark:text-white\",\n                                                    placeholder: \"Doe\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 994,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 990,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 976,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1006,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            value: signUpData.email,\n                                            onChange: (e)=>setSignUpData({\n                                                    ...signUpData,\n                                                    email: e.target.value\n                                                }),\n                                            className: \"w-full px-4 py-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-slate-700 dark:text-white\",\n                                            placeholder: \"<EMAIL>\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1009,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 1005,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1020,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"password\",\n                                            value: signUpData.password,\n                                            onChange: (e)=>setSignUpData({\n                                                    ...signUpData,\n                                                    password: e.target.value\n                                                }),\n                                            className: \"w-full px-4 py-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-slate-700 dark:text-white\",\n                                            placeholder: \"Create a strong password\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1023,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 1019,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                            children: \"Confirm Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1034,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"password\",\n                                            value: signUpData.confirmPassword,\n                                            onChange: (e)=>setSignUpData({\n                                                    ...signUpData,\n                                                    confirmPassword: e.target.value\n                                                }),\n                                            className: \"w-full px-4 py-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-slate-700 dark:text-white\",\n                                            placeholder: \"Confirm your password\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1037,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 1033,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            required: true,\n                                            className: \"rounded border-slate-300 text-emerald-600 focus:ring-emerald-500 mt-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1048,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-sm text-slate-600 dark:text-slate-300\",\n                                            children: [\n                                                \"I agree to the\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-emerald-600 hover:text-emerald-500\",\n                                                    children: \"Terms of Service\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 1055,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                \"and\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-emerald-600 hover:text-emerald-500\",\n                                                    children: \"Privacy Policy\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 1057,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1053,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 1047,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading,\n                                    className: \"w-full bg-gradient-to-r from-emerald-600 to-blue-600 text-white py-3 rounded-lg font-semibold hover:from-emerald-700 hover:to-blue-700 transition-all duration-200 disabled:opacity-50\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 1068,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Creating account...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 1067,\n                                        columnNumber: 19\n                                    }, this) : \"Create Account\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 1061,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 975,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full border-t border-slate-300 dark:border-slate-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 1080,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1079,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex justify-center text-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 bg-white dark:bg-slate-800 text-slate-500\",\n                                                children: \"Or continue with\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 1083,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1082,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 1078,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleGoogleAuth,\n                                    disabled: isLoading,\n                                    className: \"w-full mt-4 flex items-center justify-center bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-200 py-3 rounded-lg font-semibold border border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-600 transition-all duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 mr-3\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#4285F4\",\n                                                    d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 1093,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#34A853\",\n                                                    d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 1094,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#FBBC05\",\n                                                    d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 1095,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#EA4335\",\n                                                    d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 1096,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1092,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Continue with Google\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 1087,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 1077,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-slate-600 dark:text-slate-300\",\n                                children: [\n                                    \"Already have an account?\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowSignUpModal(false);\n                                            setShowSignInModal(true);\n                                        },\n                                        className: \"text-emerald-600 hover:text-emerald-500 font-medium\",\n                                        children: \"Sign in\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 1105,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 1103,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 1102,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 953,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 952,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 229,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"tFozpZAlNjbgJ/Oxct37qqbIr64=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _components_ui_notification__WEBPACK_IMPORTED_MODULE_5__.useNotifications\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/login/page.tsx\n"));

/***/ })

});