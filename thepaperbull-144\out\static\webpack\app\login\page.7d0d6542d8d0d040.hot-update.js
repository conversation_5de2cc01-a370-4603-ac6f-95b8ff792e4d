"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./app/login/page.tsx":
/*!****************************!*\
  !*** ./app/login/page.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./contexts/auth-context.tsx\");\n/* harmony import */ var _lib_auth_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth-utils */ \"(app-pages-browser)/./lib/auth-utils.ts\");\n/* harmony import */ var _components_ui_notification__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/notification */ \"(app-pages-browser)/./components/ui/notification.tsx\");\n/* harmony import */ var _lib_user_preferences_service__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/user-preferences-service */ \"(app-pages-browser)/./lib/user-preferences-service.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-pie.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,Crown,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction LoginPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { signIn, signInWithGoogle } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const notifications = (0,_components_ui_notification__WEBPACK_IMPORTED_MODULE_5__.useNotifications)();\n    // Authentication states\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [rememberMe, setRememberMe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Modal states\n    const [showSignInModal, setShowSignInModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSignUpModal, setShowSignUpModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Sign up form states\n    const [signUpData, setSignUpData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        firstName: \"\",\n        lastName: \"\",\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\"\n    });\n    // Remember me functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPage.useEffect\": ()=>{\n            const rememberedUsername = _lib_user_preferences_service__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getRememberedUsername();\n            if (rememberedUsername) {\n                setUsername(rememberedUsername);\n                setRememberMe(true);\n            }\n        }\n    }[\"LoginPage.useEffect\"], []);\n    // Check for registration success message\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPage.useEffect\": ()=>{\n            const registered = searchParams.get(\"registered\");\n            const registeredEmail = sessionStorage.getItem(\"registeredEmail\");\n            if (registered === \"true\" && registeredEmail) {\n                setSuccessMessage(\"Account created successfully! You can now log in with \".concat(registeredEmail));\n                setUsername(registeredEmail);\n                // Clear the stored email after displaying the message\n                sessionStorage.removeItem(\"registeredEmail\");\n            }\n        }\n    }[\"LoginPage.useEffect\"], [\n        searchParams\n    ]);\n    // Real Firebase authentication login\n    const handleLogin = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        setIsLoading(true);\n        try {\n            await signIn(username, password);\n            // Store username if remember me is checked\n            if (rememberMe) {\n                _lib_user_preferences_service__WEBPACK_IMPORTED_MODULE_6__[\"default\"].updateRememberedUsername(username);\n            } else {\n                _lib_user_preferences_service__WEBPACK_IMPORTED_MODULE_6__[\"default\"].updateRememberedUsername(null);\n            }\n            // Show success notification\n            notifications.success(\"Successfully signed in!\", {\n                title: \"Welcome back!\",\n                duration: 3000\n            });\n            // Redirect to trade page\n            router.push(\"/trade\");\n        } catch (error) {\n            console.error('Login error:', error);\n            const errorMessage = error.message || 'An error occurred during login';\n            notifications.error(errorMessage, {\n                title: \"Sign In Failed\",\n                duration: 5000\n            });\n            setError(errorMessage);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Real Google authentication handler\n    const handleGoogleAuth = async ()=>{\n        setIsLoading(true);\n        setError(\"\");\n        try {\n            await signInWithGoogle();\n            // Show success notification\n            notifications.success(\"Successfully signed in with Google!\", {\n                title: \"Welcome back!\",\n                duration: 3000\n            });\n            router.push(\"/trade\");\n        } catch (error) {\n            console.error('Google auth error:', error);\n            // Handle specific Firebase auth errors\n            if (error.code === 'auth/popup-closed-by-user') {\n            // User closed the popup - don't show an error, just reset loading state\n            // No notification needed as this is user-initiated\n            } else if (error.code === 'auth/popup-blocked') {\n                notifications.error('Popup was blocked by your browser. Please allow popups and try again.', {\n                    title: \"Popup Blocked\",\n                    duration: 5000\n                });\n                setError('Popup was blocked by your browser. Please allow popups and try again.');\n            } else if (error.code === 'auth/cancelled-popup-request') {\n            // User cancelled - no notification needed\n            } else {\n                notifications.error(error.message || 'Failed to sign in with Google', {\n                    title: \"Sign In Failed\",\n                    duration: 5000\n                });\n                setError(error.message || 'Failed to sign in with Google');\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Real Firebase sign up handler\n    const handleSignUp = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        if (signUpData.password !== signUpData.confirmPassword) {\n            setError(\"Passwords don't match\");\n            return;\n        }\n        setIsLoading(true);\n        try {\n            const displayName = \"\".concat(signUpData.firstName, \" \").concat(signUpData.lastName).trim();\n            await (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_4__.signUpWithEmail)(signUpData.email, signUpData.password, displayName);\n            // Show success notification\n            notifications.success(\"Account created successfully! Please sign in.\", {\n                title: \"Welcome to ThePaperBull!\",\n                duration: 4000\n            });\n            setShowSignUpModal(false);\n            setShowSignInModal(true);\n            setUsername(signUpData.email);\n            setSuccessMessage(\"Account created successfully! Please sign in.\");\n            // Reset sign up form\n            setSignUpData({\n                firstName: \"\",\n                lastName: \"\",\n                email: \"\",\n                password: \"\",\n                confirmPassword: \"\"\n            });\n        } catch (error) {\n            console.error('Sign up error:', error);\n            const errorMessage = error.message || 'Failed to create account';\n            notifications.error(errorMessage, {\n                title: \"Account Creation Failed\",\n                duration: 5000\n            });\n            setError(errorMessage);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-emerald-50 dark:from-slate-900 dark:via-slate-800 dark:to-emerald-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"relative z-50 bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl border-b border-slate-200/50 dark:border-slate-700/50 shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center py-4 sm:py-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-br from-emerald-500 via-blue-500 to-purple-600 rounded-2xl p-2 sm:p-3 shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-5 w-5 sm:h-7 sm:w-7 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-br from-emerald-400 via-blue-400 to-purple-500 rounded-2xl blur opacity-30 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3 sm:ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-xl sm:text-3xl font-black bg-gradient-to-r from-emerald-600 via-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                                    children: \"ThePaperBull\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs sm:text-sm text-slate-500 dark:text-slate-400 font-medium hidden sm:block\",\n                                                    children: \"AI-Powered Trading Platform\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                        className: \"text-slate-700 dark:text-slate-200 p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowSignInModal(true),\n                                            className: \"text-slate-700 dark:text-slate-200 hover:text-emerald-600 dark:hover:text-emerald-400 font-semibold px-4 py-2 rounded-xl hover:bg-slate-100 dark:hover:bg-slate-800 transition-all duration-200\",\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowSignUpModal(true),\n                                            className: \"bg-gradient-to-r from-emerald-600 via-blue-600 to-purple-600 text-white px-8 py-3 rounded-xl font-semibold hover:from-emerald-700 hover:via-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                                            children: \"Get Started\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this),\n                        mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden absolute top-full left-0 right-0 bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl border-b border-slate-200/50 dark:border-slate-700/50 shadow-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-6 space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowSignInModal(true);\n                                            setMobileMenuOpen(false);\n                                        },\n                                        className: \"w-full text-left text-slate-700 dark:text-slate-200 hover:text-emerald-600 dark:hover:text-emerald-400 font-semibold px-4 py-3 rounded-xl hover:bg-slate-100 dark:hover:bg-slate-800 transition-all duration-200\",\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowSignUpModal(true);\n                                            setMobileMenuOpen(false);\n                                        },\n                                        className: \"w-full bg-gradient-to-r from-emerald-600 via-blue-600 to-purple-600 text-white px-8 py-3 rounded-xl font-semibold hover:from-emerald-700 hover:via-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg\",\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center bg-gradient-to-r from-emerald-100 to-blue-100 dark:from-emerald-900/20 dark:to-blue-900/20 text-emerald-800 dark:text-emerald-300 px-3 sm:px-6 py-2 sm:py-3 rounded-full text-xs sm:text-sm font-semibold mb-6 sm:mb-8 border border-emerald-200 dark:border-emerald-800 shadow-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"\\uD83D\\uDE80 Now in Beta - Join 10,000+ Elite Traders\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sm:hidden\",\n                                            children: \"\\uD83D\\uDE80 Join 10K+ Traders\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-3 w-3 sm:h-4 sm:w-4 ml-1 sm:ml-2 text-yellow-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl sm:text-6xl md:text-8xl font-black text-slate-900 dark:text-white mb-6 sm:mb-8 leading-tight px-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block sm:inline\",\n                                            children: \"Master Crypto Trading with\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block bg-gradient-to-r from-emerald-500 via-blue-500 via-purple-500 to-pink-500 bg-clip-text text-transparent animate-gradient\",\n                                            children: \"AI-Powered Paper Trading\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-base sm:text-2xl text-slate-600 dark:text-slate-300 mb-8 sm:mb-12 max-w-4xl mx-auto leading-relaxed font-light px-4\",\n                                    children: [\n                                        \"The world's most advanced paper trading platform. Practice with\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-emerald-600 dark:text-emerald-400\",\n                                            children: \" real-time Binance data\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this),\n                                        \",\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-blue-600 dark:text-blue-400\",\n                                            children: \" TradingView charts\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this),\n                                        \", and\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-purple-600 dark:text-purple-400\",\n                                            children: \" professional trading tools\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this),\n                                        \".\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \" Master crypto futures trading risk-free with $10,000 virtual capital.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap items-center justify-center gap-3 sm:gap-8 mb-8 sm:mb-12 text-xs sm:text-sm text-slate-500 dark:text-slate-400 px-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center bg-white dark:bg-slate-800 px-3 sm:px-4 py-2 rounded-full shadow-md border border-slate-200 dark:border-slate-700 hover:shadow-lg transition-shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-3 w-3 sm:h-4 sm:w-4 text-emerald-500 mr-1 sm:mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:inline\",\n                                                    children: \"Firebase Security\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sm:hidden\",\n                                                    children: \"Secure\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center bg-white dark:bg-slate-800 px-3 sm:px-4 py-2 rounded-full shadow-md border border-slate-200 dark:border-slate-700 hover:shadow-lg transition-shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-3 w-3 sm:h-4 sm:w-4 text-blue-500 mr-1 sm:mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:inline\",\n                                                    children: \"Binance Live Data\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sm:hidden\",\n                                                    children: \"Live Data\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center bg-white dark:bg-slate-800 px-3 sm:px-4 py-2 rounded-full shadow-md border border-slate-200 dark:border-slate-700 hover:shadow-lg transition-shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-3 w-3 sm:h-4 sm:w-4 text-purple-500 mr-1 sm:mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:inline\",\n                                                    children: \"TradingView Charts\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sm:hidden\",\n                                                    children: \"Pro Charts\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center bg-white dark:bg-slate-800 px-3 sm:px-4 py-2 rounded-full shadow-md border border-slate-200 dark:border-slate-700 hover:shadow-lg transition-shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-3 w-3 sm:h-4 sm:w-4 text-pink-500 mr-1 sm:mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:inline\",\n                                                    children: \"10K+ Traders\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sm:hidden\",\n                                                    children: \"10K+ Users\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col gap-4 sm:gap-6 justify-center items-center mb-12 sm:mb-16 px-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowSignUpModal(true),\n                                            className: \"group relative bg-gradient-to-r from-emerald-600 via-blue-600 to-purple-600 text-white px-8 sm:px-12 py-4 sm:py-5 rounded-2xl font-bold text-lg sm:text-xl hover:from-emerald-700 hover:via-blue-700 hover:to-purple-700 transition-all duration-300 shadow-2xl hover:shadow-3xl transform hover:-translate-y-2 hover:scale-105 border-2 border-white/20 w-full sm:w-auto\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"relative z-10 flex items-center justify-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6 group-hover:animate-bounce\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Start Trading Free\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"inline-block ml-2 sm:ml-3 h-5 w-5 sm:h-6 sm:w-6 group-hover:translate-x-1 transition-transform\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-r from-emerald-400 via-blue-400 to-purple-400 rounded-2xl blur opacity-30 group-hover:opacity-50 transition-opacity\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleGoogleAuth,\n                                            disabled: isLoading,\n                                            className: \"group flex items-center justify-center bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-200 px-8 sm:px-10 py-4 sm:py-5 rounded-2xl font-bold text-lg sm:text-xl border-2 border-slate-200 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1 hover:scale-105 w-full sm:w-auto\",\n                                            children: [\n                                                isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-5 w-5 sm:h-6 sm:w-6 border-b-2 border-emerald-600 mr-2 sm:mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5 sm:h-6 sm:w-6 mr-2 sm:mr-3 group-hover:scale-110 transition-transform\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fill: \"#4285F4\",\n                                                            d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fill: \"#34A853\",\n                                                            d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fill: \"#FBBC05\",\n                                                            d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fill: \"#EA4335\",\n                                                            d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"group-hover:text-emerald-600 dark:group-hover:text-emerald-400 transition-colors\",\n                                                    children: \"Continue with Google\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8 text-xs sm:text-sm text-slate-500 dark:text-slate-400 px-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center bg-white/50 dark:bg-slate-800/50 px-3 py-1 rounded-full backdrop-blur-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-3 w-3 sm:h-4 sm:w-4 text-emerald-500 mr-1 sm:mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"No Credit Card Required\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center bg-white/50 dark:bg-slate-800/50 px-3 py-1 rounded-full backdrop-blur-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-3 w-3 sm:h-4 sm:w-4 text-emerald-500 mr-1 sm:mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Real Binance Data\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center bg-white/50 dark:bg-slate-800/50 px-3 py-1 rounded-full backdrop-blur-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-3 w-3 sm:h-4 sm:w-4 text-emerald-500 mr-1 sm:mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"$10K Virtual Capital\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-20 left-10 w-72 h-72 bg-emerald-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-40 right-10 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -bottom-8 left-20 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 305,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-20 bg-white dark:bg-slate-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12 sm:mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl sm:text-4xl font-bold text-slate-900 dark:text-white mb-3 sm:mb-4 px-4\",\n                                    children: \"Everything You Need to Master Trading\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-base sm:text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto px-4\",\n                                    children: \"Professional-grade tools and insights to help you become a better trader\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8\",\n                            children: [\n                                {\n                                    icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                    title: \"Professional Charting\",\n                                    description: \"TradingView integration with 100+ technical indicators, drawing tools, and multi-timeframe analysis\",\n                                    gradient: \"from-emerald-600 to-emerald-700\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                                    title: \"Real-Time Market Data\",\n                                    description: \"Live cryptocurrency prices from Binance with WebSocket connections for instant updates\",\n                                    gradient: \"from-blue-600 to-blue-700\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                                    title: \"Futures Trading Simulation\",\n                                    description: \"Practice crypto futures with leverage up to 125x using real market conditions and $10K virtual capital\",\n                                    gradient: \"from-purple-600 to-purple-700\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                    title: \"Advanced Risk Management\",\n                                    description: \"Stop-loss, take-profit orders, position sizing calculator, and portfolio risk analytics\",\n                                    gradient: \"from-orange-600 to-orange-700\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                                    title: \"Performance Analytics\",\n                                    description: \"Detailed P&L tracking, win rate analysis, and comprehensive trading statistics dashboard\",\n                                    gradient: \"from-pink-600 to-pink-700\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                                    title: \"Instant Order Execution\",\n                                    description: \"Lightning-fast order placement with market and limit orders, plus advanced order types\",\n                                    gradient: \"from-cyan-600 to-cyan-700\"\n                                }\n                            ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group bg-white dark:bg-slate-800 rounded-2xl p-6 sm:p-8 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r \".concat(feature.gradient, \" rounded-xl p-3 w-fit mb-4 sm:mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg sm:text-xl font-bold text-slate-900 dark:text-white mb-3 group-hover:text-emerald-600 dark:group-hover:text-emerald-400 transition-colors\",\n                                            children: feature.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm sm:text-base text-slate-600 dark:text-slate-300 leading-relaxed\",\n                                            children: feature.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 425,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 415,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 414,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-20 bg-gradient-to-r from-emerald-600 via-blue-600 to-purple-600 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-xl animate-float\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 484,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-10 right-10 w-24 h-24 bg-white rounded-full blur-xl animate-float animation-delay-2000\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-white rounded-full blur-2xl animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 483,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-8 sm:mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl sm:text-4xl font-bold text-white mb-3 sm:mb-4\",\n                                        children: \"Trusted by Crypto Traders Worldwide\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-base sm:text-xl text-emerald-100 max-w-2xl mx-auto\",\n                                        children: \"Join thousands of successful traders who've mastered crypto futures with ThePaperBull\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8 text-center\",\n                                children: [\n                                    {\n                                        number: \"10,000+\",\n                                        label: \"Active Traders\",\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n                                    },\n                                    {\n                                        number: \"$50M+\",\n                                        label: \"Virtual Volume Traded\",\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n                                    },\n                                    {\n                                        number: \"99.9%\",\n                                        label: \"Platform Uptime\",\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n                                    },\n                                    {\n                                        number: \"24/7\",\n                                        label: \"Market Access\",\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"]\n                                    }\n                                ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/10 backdrop-blur-sm rounded-2xl p-4 sm:p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 hover:scale-105\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center mb-2 sm:mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                        className: \"h-6 w-6 sm:h-8 sm:w-8 text-white group-hover:scale-110 transition-transform\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl sm:text-4xl font-bold text-white mb-1 sm:mb-2 group-hover:text-emerald-200 transition-colors\",\n                                                    children: stat.number\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs sm:text-base text-emerald-100 font-medium\",\n                                                    children: stat.label\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 489,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 481,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-20 bg-slate-50 dark:bg-slate-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12 sm:mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl sm:text-4xl font-bold text-slate-900 dark:text-white mb-3 sm:mb-4\",\n                                    children: \"What Traders Are Saying\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 528,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-base sm:text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto\",\n                                    children: \"Real feedback from traders who've improved their skills with ThePaperBull\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 527,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8\",\n                            children: [\n                                {\n                                    name: \"Alex Chen\",\n                                    role: \"Crypto Day Trader\",\n                                    content: \"ThePaperBull helped me master futures trading without risking real money. The TradingView integration and real-time data are incredible. I'm now profitable in live trading!\",\n                                    rating: 5,\n                                    avatar: \"AC\"\n                                },\n                                {\n                                    name: \"Sarah Martinez\",\n                                    role: \"Portfolio Manager\",\n                                    content: \"The risk management tools and analytics dashboard are professional-grade. I use it to test new strategies before implementing them with client funds. Absolutely essential.\",\n                                    rating: 5,\n                                    avatar: \"SM\"\n                                },\n                                {\n                                    name: \"Michael Thompson\",\n                                    role: \"Trading Educator\",\n                                    content: \"I recommend ThePaperBull to all my students. The platform's accuracy with Binance data and comprehensive features make it the best paper trading platform available.\",\n                                    rating: 5,\n                                    avatar: \"MT\"\n                                }\n                            ].map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-slate-800 rounded-2xl p-6 sm:p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-200 dark:border-slate-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-r from-emerald-600 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4\",\n                                                    children: testimonial.avatar\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-bold text-slate-900 dark:text-white\",\n                                                            children: testimonial.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                                            children: testimonial.role\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex mb-4\",\n                                            children: [\n                                                ...Array(testimonial.rating)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"h-5 w-5 text-yellow-400 fill-current\"\n                                                }, i, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-6 w-6 text-emerald-600 mb-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600 dark:text-slate-300 leading-relaxed italic\",\n                                            children: [\n                                                '\"',\n                                                testimonial.content,\n                                                '\"'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 560,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 536,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 526,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 525,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-20 bg-gradient-to-br from-slate-900 via-emerald-900 to-blue-900 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-emerald-500/20 to-blue-500/20\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 591,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-20 left-20 w-96 h-96 bg-emerald-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 592,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-20 right-20 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse animation-delay-2000\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 593,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 590,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-6xl mx-auto text-center px-4 sm:px-6 lg:px-8 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center bg-gradient-to-r from-emerald-500/20 to-blue-500/20 text-emerald-300 px-3 sm:px-6 py-2 sm:py-3 rounded-full text-xs sm:text-sm font-semibold mb-6 sm:mb-8 border border-emerald-500/30 animate-pulse-glow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:inline\",\n                                        children: \"\\uD83D\\uDE80 Start with $10,000 Virtual Capital - No Credit Card Required\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sm:hidden\",\n                                        children: \"\\uD83D\\uDE80 $10K Virtual Capital\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 597,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl sm:text-5xl md:text-7xl font-black text-white mb-6 sm:mb-8 leading-tight px-2\",\n                                children: [\n                                    \"Ready to Master\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block bg-gradient-to-r from-emerald-400 via-blue-400 to-purple-400 bg-clip-text text-transparent animate-gradient\",\n                                        children: \"Crypto Futures Trading?\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-base sm:text-xl text-slate-300 mb-8 sm:mb-12 max-w-3xl mx-auto leading-relaxed px-4\",\n                                children: [\n                                    \"Join 10,000+ traders who've mastered crypto futures with our professional-grade paper trading platform.\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:inline\",\n                                        children: \" Practice with real Binance data, TradingView charts, and advanced risk management tools.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 610,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 mb-8 sm:mb-12 max-w-4xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center bg-white/10 backdrop-blur-sm rounded-xl p-3 sm:p-4 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 sm:h-5 sm:w-5 text-emerald-400 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm sm:text-base font-medium\",\n                                                children: \"Real Market Data\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 619,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center bg-white/10 backdrop-blur-sm rounded-xl p-3 sm:p-4 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 sm:h-5 sm:w-5 text-emerald-400 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 622,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm sm:text-base font-medium\",\n                                                children: \"125x Leverage Simulation\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center bg-white/10 backdrop-blur-sm rounded-xl p-3 sm:p-4 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 sm:h-5 sm:w-5 text-emerald-400 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm sm:text-base font-medium\",\n                                                children: \"Advanced Analytics\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 616,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 sm:gap-6 justify-center items-center px-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowSignUpModal(true),\n                                        className: \"group relative bg-gradient-to-r from-emerald-500 to-blue-500 text-white px-8 sm:px-12 py-4 sm:py-5 rounded-2xl font-bold text-lg sm:text-xl hover:from-emerald-600 hover:to-blue-600 transition-all duration-300 shadow-2xl hover:shadow-3xl transform hover:-translate-y-2 hover:scale-105 w-full sm:w-auto overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10 flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6 group-hover:animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 637,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Start Trading Free - $10K Capital\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"inline-block ml-2 sm:ml-3 h-5 w-5 sm:h-6 sm:w-6 group-hover:translate-x-1 transition-transform\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 639,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-emerald-400 to-blue-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300 animate-shimmer\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 632,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-3 sm:gap-4 w-full sm:w-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowSignInModal(true),\n                                                className: \"group bg-white/10 backdrop-blur-sm text-white px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold text-base sm:text-lg border-2 border-white/20 hover:bg-white/20 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1 flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4 sm:h-5 sm:w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 650,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Sign In\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleGoogleAuth,\n                                                disabled: isLoading,\n                                                className: \"group bg-white text-slate-900 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold text-base sm:text-lg border-2 border-white/20 hover:bg-slate-100 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1 flex items-center justify-center\",\n                                                children: [\n                                                    isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-4 w-4 sm:h-5 sm:w-5 border-b-2 border-slate-900 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 660,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"h-4 w-4 sm:h-5 sm:w-5 mr-2\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fill: \"#4285F4\",\n                                                                d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 663,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fill: \"#34A853\",\n                                                                d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 664,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fill: \"#FBBC05\",\n                                                                d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 665,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fill: \"#EA4335\",\n                                                                d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 666,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 662,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Google\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 654,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 645,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 631,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 596,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 588,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-slate-900 text-white py-8 sm:py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-emerald-500 via-blue-500 to-purple-600 rounded-xl p-2 mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5 sm:h-6 sm:w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg sm:text-xl font-bold bg-gradient-to-r from-emerald-400 via-blue-400 to-purple-400 bg-clip-text text-transparent\",\n                                                children: \"ThePaperBull\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 686,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-400 text-xs sm:text-sm\",\n                                                children: \"AI-Powered Trading Platform\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 689,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 685,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 681,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    {\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n                                        href: \"#\"\n                                    },\n                                    {\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n                                        href: \"#\"\n                                    },\n                                    {\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n                                        href: \"#\"\n                                    },\n                                    {\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n                                        href: \"#\"\n                                    }\n                                ].map((social, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: social.href,\n                                        className: \"w-8 h-8 sm:w-10 sm:h-10 bg-slate-800 rounded-xl flex items-center justify-center hover:bg-gradient-to-r hover:from-emerald-600 hover:to-blue-600 transition-all duration-300 transform hover:scale-110\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(social.icon, {\n                                            className: \"h-4 w-4 sm:h-5 sm:w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 706,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 694,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-slate-800 pt-6 w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-400 text-xs sm:text-sm\",\n                                        children: \"\\xa9 2024 ThePaperBull. All rights reserved.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 712,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row justify-center items-center gap-2 sm:gap-4 mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-emerald-400 transition-colors text-xs sm:text-sm\",\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline text-slate-600\",\n                                                children: \"|\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-emerald-400 transition-colors text-xs sm:text-sm\",\n                                                children: \"Terms of Service\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 718,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 715,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 711,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 679,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 678,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 677,\n                columnNumber: 7\n            }, this),\n            showSignInModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-slate-800 rounded-2xl p-6 sm:p-8 w-full max-w-md relative max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowSignInModal(false),\n                            className: \"absolute top-4 right-4 text-slate-400 hover:text-slate-600 dark:hover:text-slate-200 z-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                className: \"h-5 w-5 sm:h-6 sm:w-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 733,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 729,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-6 sm:mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-emerald-600 to-blue-600 rounded-xl p-2 sm:p-3 w-fit mx-auto mb-3 sm:mb-4 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-6 w-6 sm:h-8 sm:w-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 738,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 737,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl sm:text-2xl font-bold text-slate-900 dark:text-white\",\n                                    children: \"Welcome Back, Trader\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 740,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm sm:text-base text-slate-600 dark:text-slate-300\",\n                                    children: \"Continue your trading journey with ThePaperBull\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 741,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 736,\n                            columnNumber: 13\n                        }, this),\n                        successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-emerald-50 dark:bg-emerald-900/20 border border-emerald-200 dark:border-emerald-800 text-emerald-800 dark:text-emerald-300 p-4 rounded-lg mb-6 text-sm\",\n                            children: successMessage\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 745,\n                            columnNumber: 15\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 p-4 rounded-lg mb-6 text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 751,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleLogin,\n                            className: \"space-y-4 sm:space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-xs sm:text-sm font-medium text-slate-700 dark:text-slate-300 mb-1 sm:mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 758,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            value: username,\n                                            onChange: (e)=>setUsername(e.target.value),\n                                            className: \"w-full px-3 sm:px-4 py-2 sm:py-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-slate-700 dark:text-white text-sm sm:text-base\",\n                                            placeholder: \"Enter your email\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 761,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 757,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-xs sm:text-sm font-medium text-slate-700 dark:text-slate-300 mb-1 sm:mb-2\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 772,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    value: password,\n                                                    onChange: (e)=>setPassword(e.target.value),\n                                                    className: \"w-full px-3 sm:px-4 py-2 sm:py-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-slate-700 dark:text-white pr-10 sm:pr-12 text-sm sm:text-base\",\n                                                    placeholder: \"Enter your password\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 776,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                    className: \"absolute right-2 sm:right-3 top-2 sm:top-3 text-slate-400 hover:text-slate-600 dark:hover:text-slate-200\",\n                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                        className: \"h-4 w-4 sm:h-5 sm:w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 789,\n                                                        columnNumber: 37\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                        className: \"h-4 w-4 sm:h-5 sm:w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 789,\n                                                        columnNumber: 84\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 784,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 775,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 771,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    checked: rememberMe,\n                                                    onChange: (e)=>setRememberMe(e.target.checked),\n                                                    className: \"rounded border-slate-300 text-emerald-600 focus:ring-emerald-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 796,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 text-xs sm:text-sm text-slate-600 dark:text-slate-300\",\n                                                    children: \"Remember me\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 802,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 795,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"text-xs sm:text-sm text-emerald-600 hover:text-emerald-500 text-left sm:text-right\",\n                                            children: \"Forgot password?\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 804,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 794,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading,\n                                    className: \"w-full bg-gradient-to-r from-emerald-600 to-blue-600 text-white py-2.5 sm:py-3 rounded-lg font-semibold hover:from-emerald-700 hover:to-blue-700 transition-all duration-200 disabled:opacity-50 text-sm sm:text-base\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 sm:h-5 sm:w-5 border-b-2 border-white mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 816,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Signing in...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 815,\n                                        columnNumber: 19\n                                    }, this) : \"Sign In\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 809,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 756,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 sm:mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full border-t border-slate-300 dark:border-slate-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 828,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 827,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex justify-center text-xs sm:text-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 bg-white dark:bg-slate-800 text-slate-500\",\n                                                children: \"Or continue with\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 831,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 830,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 826,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleGoogleAuth,\n                                    disabled: isLoading,\n                                    className: \"w-full mt-3 sm:mt-4 flex items-center justify-center bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-200 py-2.5 sm:py-3 rounded-lg font-semibold border border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-600 transition-all duration-200 text-sm sm:text-base\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-4 w-4 sm:h-5 sm:w-5 mr-2 sm:mr-3\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#4285F4\",\n                                                    d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 841,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#34A853\",\n                                                    d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 842,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#FBBC05\",\n                                                    d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 843,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#EA4335\",\n                                                    d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 844,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 840,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Continue with Google\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 835,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 825,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 sm:mt-6 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs sm:text-sm text-slate-600 dark:text-slate-300\",\n                                children: [\n                                    \"Don't have an account?\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowSignInModal(false);\n                                            setShowSignUpModal(true);\n                                        },\n                                        className: \"text-emerald-600 hover:text-emerald-500 font-medium\",\n                                        children: \"Sign up\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 853,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 851,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 850,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 728,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 727,\n                columnNumber: 9\n            }, this),\n            showSignUpModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-slate-800 rounded-2xl p-6 sm:p-8 w-full max-w-md relative max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowSignUpModal(false),\n                            className: \"absolute top-4 right-4 text-slate-400 hover:text-slate-600 dark:hover:text-slate-200 z-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                className: \"h-5 w-5 sm:h-6 sm:w-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 878,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 874,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-6 sm:mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-emerald-600 to-blue-600 rounded-xl p-2 sm:p-3 w-fit mx-auto mb-3 sm:mb-4 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_Crown_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-6 w-6 sm:h-8 sm:w-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 883,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 882,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl sm:text-2xl font-bold text-slate-900 dark:text-white\",\n                                    children: \"Join ThePaperBull\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 885,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm sm:text-base text-slate-600 dark:text-slate-300\",\n                                    children: \"Start mastering crypto futures with $10,000 virtual capital\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 886,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 881,\n                            columnNumber: 13\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 p-4 rounded-lg mb-6 text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 890,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSignUp,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                    children: \"First Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 898,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: signUpData.firstName,\n                                                    onChange: (e)=>setSignUpData({\n                                                            ...signUpData,\n                                                            firstName: e.target.value\n                                                        }),\n                                                    className: \"w-full px-4 py-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-slate-700 dark:text-white\",\n                                                    placeholder: \"John\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 901,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 897,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                    children: \"Last Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 911,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: signUpData.lastName,\n                                                    onChange: (e)=>setSignUpData({\n                                                            ...signUpData,\n                                                            lastName: e.target.value\n                                                        }),\n                                                    className: \"w-full px-4 py-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-slate-700 dark:text-white\",\n                                                    placeholder: \"Doe\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 914,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 910,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 896,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 926,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            value: signUpData.email,\n                                            onChange: (e)=>setSignUpData({\n                                                    ...signUpData,\n                                                    email: e.target.value\n                                                }),\n                                            className: \"w-full px-4 py-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-slate-700 dark:text-white\",\n                                            placeholder: \"<EMAIL>\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 929,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 925,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 940,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"password\",\n                                            value: signUpData.password,\n                                            onChange: (e)=>setSignUpData({\n                                                    ...signUpData,\n                                                    password: e.target.value\n                                                }),\n                                            className: \"w-full px-4 py-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-slate-700 dark:text-white\",\n                                            placeholder: \"Create a strong password\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 943,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 939,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                            children: \"Confirm Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 954,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"password\",\n                                            value: signUpData.confirmPassword,\n                                            onChange: (e)=>setSignUpData({\n                                                    ...signUpData,\n                                                    confirmPassword: e.target.value\n                                                }),\n                                            className: \"w-full px-4 py-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-slate-700 dark:text-white\",\n                                            placeholder: \"Confirm your password\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 957,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 953,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            required: true,\n                                            className: \"rounded border-slate-300 text-emerald-600 focus:ring-emerald-500 mt-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 968,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-sm text-slate-600 dark:text-slate-300\",\n                                            children: [\n                                                \"I agree to the\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-emerald-600 hover:text-emerald-500\",\n                                                    children: \"Terms of Service\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 975,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                \"and\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-emerald-600 hover:text-emerald-500\",\n                                                    children: \"Privacy Policy\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 977,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 973,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 967,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading,\n                                    className: \"w-full bg-gradient-to-r from-emerald-600 to-blue-600 text-white py-3 rounded-lg font-semibold hover:from-emerald-700 hover:to-blue-700 transition-all duration-200 disabled:opacity-50\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 988,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Creating account...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 987,\n                                        columnNumber: 19\n                                    }, this) : \"Create Account\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 981,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 895,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full border-t border-slate-300 dark:border-slate-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 1000,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 999,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex justify-center text-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 bg-white dark:bg-slate-800 text-slate-500\",\n                                                children: \"Or continue with\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 1003,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1002,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 998,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleGoogleAuth,\n                                    disabled: isLoading,\n                                    className: \"w-full mt-4 flex items-center justify-center bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-200 py-3 rounded-lg font-semibold border border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-600 transition-all duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 mr-3\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#4285F4\",\n                                                    d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 1013,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#34A853\",\n                                                    d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 1014,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#FBBC05\",\n                                                    d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 1015,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#EA4335\",\n                                                    d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 1016,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1012,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Continue with Google\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 1007,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 997,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-slate-600 dark:text-slate-300\",\n                                children: [\n                                    \"Already have an account?\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowSignUpModal(false);\n                                            setShowSignInModal(true);\n                                        },\n                                        className: \"text-emerald-600 hover:text-emerald-500 font-medium\",\n                                        children: \"Sign in\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 1025,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 1023,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 1022,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 873,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 872,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 229,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"tFozpZAlNjbgJ/Oxct37qqbIr64=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _components_ui_notification__WEBPACK_IMPORTED_MODULE_5__.useNotifications\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/login/page.tsx\n"));

/***/ })

});