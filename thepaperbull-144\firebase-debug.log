[debug] [2025-05-31T17:56:39.155Z] ----------------------------------------------------------------------
[debug] [2025-05-31T17:56:39.158Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js login --no-localhost
[debug] [2025-05-31T17:56:39.158Z] CLI Version:   14.4.0
[debug] [2025-05-31T17:56:39.158Z] Platform:      win32
[debug] [2025-05-31T17:56:39.158Z] Node Version:  v22.15.0
[debug] [2025-05-31T17:56:39.158Z] Time:          Sat May 31 2025 23:26:39 GMT+0530 (India Standard Time)
[debug] [2025-05-31T17:56:39.158Z] ----------------------------------------------------------------------
[debug] 
[info] i  Firebase optionally collects CLI and Emulator Suite usage and error reporting information to help improve our products. Data is collected in accordance with Google's privacy policy (https://policies.google.com/privacy) and is not used to identify you.
 
[info] i  To change your data collection preference at any time, run `firebase logout` and log in again. 
[debug] [2025-05-31T17:56:48.035Z] >>> [apiv2][query] POST https://auth.firebase.tools/attest [none]
[debug] [2025-05-31T17:56:48.036Z] >>> [apiv2][body] POST https://auth.firebase.tools/attest {"session_id":"8297ee07-7f34-4c9f-a243-4db6d3a4a1a2"}
[debug] [2025-05-31T17:56:48.628Z] <<< [apiv2][status] POST https://auth.firebase.tools/attest 200
[debug] [2025-05-31T17:56:48.628Z] <<< [apiv2][body] POST https://auth.firebase.tools/attest {"token":"_aKEUF0xfDkCfN5Pzr8ruNE2oviMsR5IZsuPfvT_wys"}
[info] 
[info] To sign in to the Firebase CLI:
[info] 
[info] 1. Take note of your session ID:
[info] 
[info]    8297E
[info] 
[info] 2. Visit the URL below on any device and follow the instructions to get your code:
[info] 
[info]    https://auth.firebase.tools/login?code_challenge=1tCKFBqHB_YQ0Sh4maj1aHeepcHXK7KQ4d8XzvVWi_w&session=8297ee07-7f34-4c9f-a243-4db6d3a4a1a2&attest=_aKEUF0xfDkCfN5Pzr8ruNE2oviMsR5IZsuPfvT_wys
[info] 
[info] 3. Paste or enter the authorization code below once you have it:
[info] 
[debug] [2025-05-31T17:57:40.120Z] ExitPromptError: User force closed the prompt with SIGINT
    at Interface.sigint (C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\node_modules\@inquirer\core\dist\commonjs\lib\create-prompt.js:100:37)
    at Interface.emit (node:events:518:28)
    at Interface.emit (node:domain:489:12)
    at [_ttyWrite] [as _ttyWrite] (node:internal/readline/interface:1128:18)
    at ReadStream.onkeypress (node:internal/readline/interface:267:20)
    at ReadStream.emit (node:events:530:35)
    at ReadStream.emit (node:domain:489:12)
    at emitKeys (node:internal/readline/utils:370:14)
    at emitKeys.next (<anonymous>)
    at ReadStream.onData (node:internal/readline/emitKeypressEvents:64:36)
[error] 
[error] Error: An unexpected error has occurred.
