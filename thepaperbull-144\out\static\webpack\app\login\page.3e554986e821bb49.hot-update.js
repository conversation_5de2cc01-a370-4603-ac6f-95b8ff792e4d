"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./app/login/page.tsx":
/*!****************************!*\
  !*** ./app/login/page.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./contexts/auth-context.tsx\");\n/* harmony import */ var _lib_auth_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth-utils */ \"(app-pages-browser)/./lib/auth-utils.ts\");\n/* harmony import */ var _components_ui_notification__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/notification */ \"(app-pages-browser)/./components/ui/notification.tsx\");\n/* harmony import */ var _lib_user_preferences_service__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/user-preferences-service */ \"(app-pages-browser)/./lib/user-preferences-service.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-pie.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,CheckCircle,DollarSign,Eye,EyeOff,Github,Globe,Instagram,Linkedin,Lock,Menu,PieChart,Quote,Rocket,Shield,Sparkles,Star,TrendingUp,Twitter,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction LoginPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { signIn, signInWithGoogle } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const notifications = (0,_components_ui_notification__WEBPACK_IMPORTED_MODULE_5__.useNotifications)();\n    // Authentication states\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [rememberMe, setRememberMe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Modal states\n    const [showSignInModal, setShowSignInModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSignUpModal, setShowSignUpModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Sign up form states\n    const [signUpData, setSignUpData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        firstName: \"\",\n        lastName: \"\",\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\"\n    });\n    // Remember me functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPage.useEffect\": ()=>{\n            const rememberedUsername = _lib_user_preferences_service__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getRememberedUsername();\n            if (rememberedUsername) {\n                setUsername(rememberedUsername);\n                setRememberMe(true);\n            }\n        }\n    }[\"LoginPage.useEffect\"], []);\n    // Check for registration success message\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPage.useEffect\": ()=>{\n            const registered = searchParams.get(\"registered\");\n            const registeredEmail = sessionStorage.getItem(\"registeredEmail\");\n            if (registered === \"true\" && registeredEmail) {\n                setSuccessMessage(\"Account created successfully! You can now log in with \".concat(registeredEmail));\n                setUsername(registeredEmail);\n                // Clear the stored email after displaying the message\n                sessionStorage.removeItem(\"registeredEmail\");\n            }\n        }\n    }[\"LoginPage.useEffect\"], [\n        searchParams\n    ]);\n    // Real Firebase authentication login\n    const handleLogin = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        setIsLoading(true);\n        try {\n            await signIn(username, password);\n            // Store username if remember me is checked\n            if (rememberMe) {\n                _lib_user_preferences_service__WEBPACK_IMPORTED_MODULE_6__[\"default\"].updateRememberedUsername(username);\n            } else {\n                _lib_user_preferences_service__WEBPACK_IMPORTED_MODULE_6__[\"default\"].updateRememberedUsername(null);\n            }\n            // Show success notification\n            notifications.success(\"Successfully signed in!\", {\n                title: \"Welcome back!\",\n                duration: 3000\n            });\n            // Redirect to trade page\n            router.push(\"/trade\");\n        } catch (error) {\n            console.error('Login error:', error);\n            const errorMessage = error.message || 'An error occurred during login';\n            notifications.error(errorMessage, {\n                title: \"Sign In Failed\",\n                duration: 5000\n            });\n            setError(errorMessage);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Real Google authentication handler\n    const handleGoogleAuth = async ()=>{\n        setIsLoading(true);\n        setError(\"\");\n        try {\n            await signInWithGoogle();\n            // Show success notification\n            notifications.success(\"Successfully signed in with Google!\", {\n                title: \"Welcome back!\",\n                duration: 3000\n            });\n            router.push(\"/trade\");\n        } catch (error) {\n            console.error('Google auth error:', error);\n            // Handle specific Firebase auth errors\n            if (error.code === 'auth/popup-closed-by-user') {\n            // User closed the popup - don't show an error, just reset loading state\n            // No notification needed as this is user-initiated\n            } else if (error.code === 'auth/popup-blocked') {\n                notifications.error('Popup was blocked by your browser. Please allow popups and try again.', {\n                    title: \"Popup Blocked\",\n                    duration: 5000\n                });\n                setError('Popup was blocked by your browser. Please allow popups and try again.');\n            } else if (error.code === 'auth/cancelled-popup-request') {\n            // User cancelled - no notification needed\n            } else {\n                notifications.error(error.message || 'Failed to sign in with Google', {\n                    title: \"Sign In Failed\",\n                    duration: 5000\n                });\n                setError(error.message || 'Failed to sign in with Google');\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Real Firebase sign up handler\n    const handleSignUp = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        if (signUpData.password !== signUpData.confirmPassword) {\n            setError(\"Passwords don't match\");\n            return;\n        }\n        setIsLoading(true);\n        try {\n            const displayName = \"\".concat(signUpData.firstName, \" \").concat(signUpData.lastName).trim();\n            await (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_4__.signUpWithEmail)(signUpData.email, signUpData.password, displayName);\n            // Show success notification\n            notifications.success(\"Account created successfully! Please sign in.\", {\n                title: \"Welcome to ThePaperBull!\",\n                duration: 4000\n            });\n            setShowSignUpModal(false);\n            setShowSignInModal(true);\n            setUsername(signUpData.email);\n            setSuccessMessage(\"Account created successfully! Please sign in.\");\n            // Reset sign up form\n            setSignUpData({\n                firstName: \"\",\n                lastName: \"\",\n                email: \"\",\n                password: \"\",\n                confirmPassword: \"\"\n            });\n        } catch (error) {\n            console.error('Sign up error:', error);\n            const errorMessage = error.message || 'Failed to create account';\n            notifications.error(errorMessage, {\n                title: \"Account Creation Failed\",\n                duration: 5000\n            });\n            setError(errorMessage);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-emerald-50 dark:from-slate-900 dark:via-slate-800 dark:to-emerald-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"relative z-50 bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl border-b border-slate-200/50 dark:border-slate-700/50 shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center py-4 sm:py-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-br from-emerald-500 via-blue-500 to-purple-600 rounded-2xl p-2 sm:p-3 shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-5 w-5 sm:h-7 sm:w-7 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-br from-emerald-400 via-blue-400 to-purple-500 rounded-2xl blur opacity-30 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3 sm:ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-xl sm:text-3xl font-black bg-gradient-to-r from-emerald-600 via-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                                    children: \"ThePaperBull\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs sm:text-sm text-slate-500 dark:text-slate-400 font-medium hidden sm:block\",\n                                                    children: \"AI-Powered Trading Platform\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                        className: \"text-slate-700 dark:text-slate-200 p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowSignInModal(true),\n                                            className: \"text-slate-700 dark:text-slate-200 hover:text-emerald-600 dark:hover:text-emerald-400 font-semibold px-4 py-2 rounded-xl hover:bg-slate-100 dark:hover:bg-slate-800 transition-all duration-200\",\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowSignUpModal(true),\n                                            className: \"bg-gradient-to-r from-emerald-600 via-blue-600 to-purple-600 text-white px-8 py-3 rounded-xl font-semibold hover:from-emerald-700 hover:via-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                                            children: \"Get Started\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this),\n                        mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden absolute top-full left-0 right-0 bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl border-b border-slate-200/50 dark:border-slate-700/50 shadow-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-6 space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowSignInModal(true);\n                                            setMobileMenuOpen(false);\n                                        },\n                                        className: \"w-full text-left text-slate-700 dark:text-slate-200 hover:text-emerald-600 dark:hover:text-emerald-400 font-semibold px-4 py-3 rounded-xl hover:bg-slate-100 dark:hover:bg-slate-800 transition-all duration-200\",\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowSignUpModal(true);\n                                            setMobileMenuOpen(false);\n                                        },\n                                        className: \"w-full bg-gradient-to-r from-emerald-600 via-blue-600 to-purple-600 text-white px-8 py-3 rounded-xl font-semibold hover:from-emerald-700 hover:via-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg\",\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative overflow-hidden min-h-[90vh] flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50/30 to-emerald-50/30 dark:from-slate-900 dark:via-slate-800/50 dark:to-emerald-900/20\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-emerald-400/20 to-blue-400/20 rounded-full blur-3xl animate-float\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl animate-float animation-delay-2000\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-purple-400/10 to-emerald-400/10 rounded-full blur-3xl animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 xl:grid-cols-2 gap-8 xl:gap-16 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center xl:text-left order-2 xl:order-1 space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center bg-gradient-to-r from-emerald-500/10 to-blue-500/10 backdrop-blur-sm border border-emerald-500/20 text-emerald-700 dark:text-emerald-300 px-6 py-3 rounded-full text-sm font-semibold shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-emerald-500 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"hidden sm:inline\",\n                                                        children: \"\\uD83D\\uDE80 Live Beta • Join 10,000+ Traders\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sm:hidden\",\n                                                        children: \"\\uD83D\\uDE80 Live Beta\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2 text-yellow-500 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-5xl sm:text-6xl lg:text-7xl xl:text-8xl font-black leading-[0.9] tracking-tight\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"block text-slate-900 dark:text-white mb-2\",\n                                                            children: \"Master\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"block bg-gradient-to-r from-emerald-600 via-blue-600 to-purple-600 bg-clip-text text-transparent animate-gradient\",\n                                                            children: \"Crypto Futures\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"block text-slate-900 dark:text-white text-4xl sm:text-5xl lg:text-6xl xl:text-7xl mt-2\",\n                                                            children: \"Trading\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl sm:text-2xl lg:text-3xl text-slate-600 dark:text-slate-300 font-light leading-relaxed max-w-3xl mx-auto xl:mx-0\",\n                                                    children: [\n                                                        \"Practice with \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-emerald-600 dark:text-emerald-400\",\n                                                            children: \"real Binance data\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 33\n                                                        }, this),\n                                                        \" and \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-blue-600 dark:text-blue-400\",\n                                                            children: \"TradingView charts\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 133\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 sm:grid-cols-3 gap-4 max-w-2xl mx-auto xl:mx-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"group bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl rounded-2xl p-6 border border-slate-200/50 dark:border-slate-700/50 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center text-center space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-2xl font-black text-slate-900 dark:text-white\",\n                                                                        children: \"$10,000\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-slate-600 dark:text-slate-400 font-medium\",\n                                                                        children: \"Virtual Capital\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 357,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"group bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl rounded-2xl p-6 border border-slate-200/50 dark:border-slate-700/50 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center text-center space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-2xl font-black text-slate-900 dark:text-white\",\n                                                                        children: \"Real-Time\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 368,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-slate-600 dark:text-slate-400 font-medium\",\n                                                                        children: \"Market Data\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 369,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"group bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl rounded-2xl p-6 border border-slate-200/50 dark:border-slate-700/50 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center text-center space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-2xl font-black text-slate-900 dark:text-white\",\n                                                                        children: \"125x\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-slate-600 dark:text-slate-400 font-medium\",\n                                                                        children: \"Max Leverage\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 381,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-4 justify-center xl:justify-start max-w-lg mx-auto xl:mx-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowSignUpModal(true),\n                                                    className: \"group relative bg-gradient-to-r from-emerald-600 via-blue-600 to-purple-600 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:from-emerald-700 hover:via-blue-700 hover:to-purple-700 transition-all duration-300 shadow-2xl hover:shadow-3xl transform hover:-translate-y-2 hover:scale-105 overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"relative z-10 flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"mr-3 h-6 w-6 group-hover:animate-bounce\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Start Trading Free\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"inline-block ml-3 h-6 w-6 group-hover:translate-x-1 transition-transform\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 396,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-r from-emerald-400 via-blue-400 to-purple-400 opacity-0 group-hover:opacity-30 transition-opacity duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300 animate-shimmer\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowSignInModal(true),\n                                                    className: \"group flex items-center justify-center bg-white/90 dark:bg-slate-800/90 backdrop-blur-xl text-slate-700 dark:text-slate-200 px-8 py-4 rounded-2xl font-semibold text-lg border border-slate-200/50 dark:border-slate-600/50 hover:bg-white dark:hover:bg-slate-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"mr-3 h-5 w-5 group-hover:scale-110 transition-transform\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Sign In\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center xl:justify-start space-x-6 text-slate-500 dark:text-slate-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-emerald-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Free Forever\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"No Downloads\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-purple-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 422,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Instant Access\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-5 order-1 lg:order-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/95 dark:bg-slate-800/95 backdrop-blur-xl rounded-2xl p-6 shadow-2xl border border-slate-200/50 dark:border-slate-700/50 max-w-md mx-auto lg:max-w-none sticky top-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-emerald-600 to-blue-600 rounded-xl mb-3 shadow-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-6 w-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-bold text-slate-900 dark:text-white mb-1\",\n                                                        children: \"Join ThePaperBull\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-slate-600 dark:text-slate-300\",\n                                                        children: \"Professional crypto futures trading\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-3 gap-3 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-3 bg-emerald-50 dark:bg-emerald-900/20 rounded-lg border border-emerald-200/50 dark:border-emerald-700/50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-emerald-600 dark:text-emerald-400\",\n                                                                children: \"$10K\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-emerald-600/70 dark:text-emerald-400/70\",\n                                                                children: \"Capital\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200/50 dark:border-blue-700/50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-blue-600 dark:text-blue-400\",\n                                                                children: \"125x\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-blue-600/70 dark:text-blue-400/70\",\n                                                                children: \"Leverage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200/50 dark:border-purple-700/50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-purple-600 dark:text-purple-400\",\n                                                                children: \"Free\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-purple-600/70 dark:text-purple-400/70\",\n                                                                children: \"Forever\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 452,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleGoogleAuth,\n                                                        disabled: isLoading,\n                                                        className: \"w-full flex items-center justify-center bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-200 px-6 py-3.5 rounded-xl font-semibold border border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-600 transition-all duration-200 shadow-md hover:shadow-lg group\",\n                                                        children: [\n                                                            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-slate-700 dark:border-slate-200 mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"h-5 w-5 mr-3 group-hover:scale-110 transition-transform\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fill: \"#4285F4\",\n                                                                        d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 467,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fill: \"#34A853\",\n                                                                        d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 468,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fill: \"#FBBC05\",\n                                                                        d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 469,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fill: \"#EA4335\",\n                                                                        d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 470,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Continue with Google\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 flex items-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full border-t border-slate-300 dark:border-slate-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 478,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 477,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative flex justify-center text-sm\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-3 bg-white/95 dark:bg-slate-800/95 text-slate-500\",\n                                                                    children: \"Or\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 481,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowSignInModal(true),\n                                                                className: \"flex items-center justify-center bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-200 px-4 py-3 rounded-xl font-medium hover:bg-slate-200 dark:hover:bg-slate-600 transition-all duration-200 group\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4 group-hover:scale-110 transition-transform\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 490,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Sign In\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 486,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowSignUpModal(true),\n                                                                className: \"flex items-center justify-center bg-gradient-to-r from-emerald-600 to-blue-600 text-white px-4 py-3 rounded-xl font-medium hover:from-emerald-700 hover:to-blue-700 transition-all duration-200 group\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4 group-hover:scale-110 transition-transform\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 497,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Sign Up\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 493,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6 pt-4 border-t border-slate-200/50 dark:border-slate-700/50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center text-xs text-slate-500 dark:text-slate-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-3 w-3 text-emerald-500 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Secured by Firebase • 10,000+ Traders\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 507,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-20 left-10 w-72 h-72 bg-emerald-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 517,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-40 right-10 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -bottom-8 left-20 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 519,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 516,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 305,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-20 bg-white dark:bg-slate-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12 sm:mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl sm:text-4xl font-bold text-slate-900 dark:text-white mb-3 sm:mb-4 px-4\",\n                                    children: \"Everything You Need to Master Trading\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-base sm:text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto px-4\",\n                                    children: \"Professional-grade tools and insights to help you become a better trader\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 526,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8\",\n                            children: [\n                                {\n                                    icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                    title: \"Professional Charting\",\n                                    description: \"TradingView integration with 100+ technical indicators, drawing tools, and multi-timeframe analysis\",\n                                    gradient: \"from-emerald-600 to-emerald-700\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                    title: \"Real-Time Market Data\",\n                                    description: \"Live cryptocurrency prices from Binance with WebSocket connections for instant updates\",\n                                    gradient: \"from-blue-600 to-blue-700\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                    title: \"Futures Trading Simulation\",\n                                    description: \"Practice crypto futures with leverage up to 125x using real market conditions and $10K virtual capital\",\n                                    gradient: \"from-purple-600 to-purple-700\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                    title: \"Advanced Risk Management\",\n                                    description: \"Stop-loss, take-profit orders, position sizing calculator, and portfolio risk analytics\",\n                                    gradient: \"from-orange-600 to-orange-700\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                                    title: \"Performance Analytics\",\n                                    description: \"Detailed P&L tracking, win rate analysis, and comprehensive trading statistics dashboard\",\n                                    gradient: \"from-pink-600 to-pink-700\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                    title: \"Instant Order Execution\",\n                                    description: \"Lightning-fast order placement with market and limit orders, plus advanced order types\",\n                                    gradient: \"from-cyan-600 to-cyan-700\"\n                                }\n                            ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group bg-white dark:bg-slate-800 rounded-2xl p-6 sm:p-8 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r \".concat(feature.gradient, \" rounded-xl p-3 w-fit mb-4 sm:mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 576,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg sm:text-xl font-bold text-slate-900 dark:text-white mb-3 group-hover:text-emerald-600 dark:group-hover:text-emerald-400 transition-colors\",\n                                            children: feature.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm sm:text-base text-slate-600 dark:text-slate-300 leading-relaxed\",\n                                            children: feature.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 581,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 574,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 535,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 525,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 524,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-20 bg-gradient-to-r from-emerald-600 via-blue-600 to-purple-600 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-xl animate-float\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-10 right-10 w-24 h-24 bg-white rounded-full blur-xl animate-float animation-delay-2000\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-white rounded-full blur-2xl animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 596,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-8 sm:mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl sm:text-4xl font-bold text-white mb-3 sm:mb-4\",\n                                        children: \"Trusted by Crypto Traders Worldwide\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-base sm:text-xl text-emerald-100 max-w-2xl mx-auto\",\n                                        children: \"Join thousands of successful traders who've mastered crypto futures with ThePaperBull\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 600,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8 text-center\",\n                                children: [\n                                    {\n                                        number: \"10,000+\",\n                                        label: \"Active Traders\",\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n                                    },\n                                    {\n                                        number: \"$50M+\",\n                                        label: \"Virtual Volume Traded\",\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                                    },\n                                    {\n                                        number: \"99.9%\",\n                                        label: \"Platform Uptime\",\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                    },\n                                    {\n                                        number: \"24/7\",\n                                        label: \"Market Access\",\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"]\n                                    }\n                                ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/10 backdrop-blur-sm rounded-2xl p-4 sm:p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 hover:scale-105\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center mb-2 sm:mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                        className: \"h-6 w-6 sm:h-8 sm:w-8 text-white group-hover:scale-110 transition-transform\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 618,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl sm:text-4xl font-bold text-white mb-1 sm:mb-2 group-hover:text-emerald-200 transition-colors\",\n                                                    children: stat.number\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 621,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs sm:text-base text-emerald-100 font-medium\",\n                                                    children: stat.label\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 624,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 617,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 616,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 609,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 599,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 591,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-20 bg-slate-50 dark:bg-slate-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12 sm:mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl sm:text-4xl font-bold text-slate-900 dark:text-white mb-3 sm:mb-4\",\n                                    children: \"What Traders Are Saying\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-base sm:text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto\",\n                                    children: \"Real feedback from traders who've improved their skills with ThePaperBull\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 641,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 637,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8\",\n                            children: [\n                                {\n                                    name: \"Alex Chen\",\n                                    role: \"Crypto Day Trader\",\n                                    content: \"ThePaperBull helped me master futures trading without risking real money. The TradingView integration and real-time data are incredible. I'm now profitable in live trading!\",\n                                    rating: 5,\n                                    avatar: \"AC\"\n                                },\n                                {\n                                    name: \"Sarah Martinez\",\n                                    role: \"Portfolio Manager\",\n                                    content: \"The risk management tools and analytics dashboard are professional-grade. I use it to test new strategies before implementing them with client funds. Absolutely essential.\",\n                                    rating: 5,\n                                    avatar: \"SM\"\n                                },\n                                {\n                                    name: \"Michael Thompson\",\n                                    role: \"Trading Educator\",\n                                    content: \"I recommend ThePaperBull to all my students. The platform's accuracy with Binance data and comprehensive features make it the best paper trading platform available.\",\n                                    rating: 5,\n                                    avatar: \"MT\"\n                                }\n                            ].map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-slate-800 rounded-2xl p-6 sm:p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-200 dark:border-slate-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-r from-emerald-600 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4\",\n                                                    children: testimonial.avatar\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 672,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-bold text-slate-900 dark:text-white\",\n                                                            children: testimonial.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 676,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                                            children: testimonial.role\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 677,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 671,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex mb-4\",\n                                            children: [\n                                                ...Array(testimonial.rating)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-5 w-5 text-yellow-400 fill-current\"\n                                                }, i, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 683,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 681,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-6 w-6 text-emerald-600 mb-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 687,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600 dark:text-slate-300 leading-relaxed italic\",\n                                            children: [\n                                                '\"',\n                                                testimonial.content,\n                                                '\"'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 670,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 646,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 636,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 635,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 sm:py-20 bg-gradient-to-br from-slate-900 via-emerald-900 to-blue-900 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-emerald-500/20 to-blue-500/20\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 701,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-20 left-20 w-96 h-96 bg-emerald-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 702,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-20 right-20 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse animation-delay-2000\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 703,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 700,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-6xl mx-auto text-center px-4 sm:px-6 lg:px-8 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center bg-gradient-to-r from-emerald-500/20 to-blue-500/20 text-emerald-300 px-3 sm:px-6 py-2 sm:py-3 rounded-full text-xs sm:text-sm font-semibold mb-6 sm:mb-8 border border-emerald-500/30 animate-pulse-glow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 708,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:inline\",\n                                        children: \"\\uD83D\\uDE80 Start with $10,000 Virtual Capital - No Credit Card Required\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 709,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sm:hidden\",\n                                        children: \"\\uD83D\\uDE80 $10K Virtual Capital\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 710,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 707,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl sm:text-5xl md:text-7xl font-black text-white mb-6 sm:mb-8 leading-tight px-2\",\n                                children: [\n                                    \"Ready to Master\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block bg-gradient-to-r from-emerald-400 via-blue-400 to-purple-400 bg-clip-text text-transparent animate-gradient\",\n                                        children: \"Crypto Futures Trading?\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 715,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 713,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-base sm:text-xl text-slate-300 mb-8 sm:mb-12 max-w-3xl mx-auto leading-relaxed px-4\",\n                                children: [\n                                    \"Join 10,000+ traders who've mastered crypto futures with our professional-grade paper trading platform.\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:inline\",\n                                        children: \" Practice with real Binance data, TradingView charts, and advanced risk management tools.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 722,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 720,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 mb-8 sm:mb-12 max-w-4xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center bg-white/10 backdrop-blur-sm rounded-xl p-3 sm:p-4 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"h-4 w-4 sm:h-5 sm:w-5 text-emerald-400 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm sm:text-base font-medium\",\n                                                children: \"Real Market Data\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 729,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 727,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center bg-white/10 backdrop-blur-sm rounded-xl p-3 sm:p-4 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"h-4 w-4 sm:h-5 sm:w-5 text-emerald-400 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 732,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm sm:text-base font-medium\",\n                                                children: \"125x Leverage Simulation\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 731,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center bg-white/10 backdrop-blur-sm rounded-xl p-3 sm:p-4 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"h-4 w-4 sm:h-5 sm:w-5 text-emerald-400 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 736,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm sm:text-base font-medium\",\n                                                children: \"Advanced Analytics\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 737,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 735,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 726,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 sm:gap-6 justify-center items-center px-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowSignUpModal(true),\n                                        className: \"group relative bg-gradient-to-r from-emerald-500 to-blue-500 text-white px-8 sm:px-12 py-4 sm:py-5 rounded-2xl font-bold text-lg sm:text-xl hover:from-emerald-600 hover:to-blue-600 transition-all duration-300 shadow-2xl hover:shadow-3xl transform hover:-translate-y-2 hover:scale-105 w-full sm:w-auto overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10 flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6 group-hover:animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 747,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Start Trading Free - $10K Capital\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"inline-block ml-2 sm:ml-3 h-5 w-5 sm:h-6 sm:w-6 group-hover:translate-x-1 transition-transform\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 749,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 746,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-emerald-400 to-blue-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 751,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300 animate-shimmer\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 752,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 742,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-3 sm:gap-4 w-full sm:w-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowSignInModal(true),\n                                                className: \"group bg-white/10 backdrop-blur-sm text-white px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold text-base sm:text-lg border-2 border-white/20 hover:bg-white/20 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1 flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4 sm:h-5 sm:w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Sign In\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 756,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleGoogleAuth,\n                                                disabled: isLoading,\n                                                className: \"group bg-white text-slate-900 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold text-base sm:text-lg border-2 border-white/20 hover:bg-slate-100 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1 flex items-center justify-center\",\n                                                children: [\n                                                    isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-4 w-4 sm:h-5 sm:w-5 border-b-2 border-slate-900 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 770,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"h-4 w-4 sm:h-5 sm:w-5 mr-2\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fill: \"#4285F4\",\n                                                                d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 773,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fill: \"#34A853\",\n                                                                d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 774,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fill: \"#FBBC05\",\n                                                                d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 775,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fill: \"#EA4335\",\n                                                                d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 776,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 772,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Google\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 764,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 755,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 741,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 706,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 698,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-slate-900 text-white py-8 sm:py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-emerald-500 via-blue-500 to-purple-600 rounded-xl p-2 mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5 sm:h-6 sm:w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 793,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 792,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg sm:text-xl font-bold bg-gradient-to-r from-emerald-400 via-blue-400 to-purple-400 bg-clip-text text-transparent\",\n                                                children: \"ThePaperBull\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 796,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-400 text-xs sm:text-sm\",\n                                                children: \"AI-Powered Trading Platform\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 799,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 795,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 791,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    {\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                                        href: \"#\"\n                                    },\n                                    {\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n                                        href: \"#\"\n                                    },\n                                    {\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n                                        href: \"#\"\n                                    },\n                                    {\n                                        icon: _barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n                                        href: \"#\"\n                                    }\n                                ].map((social, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: social.href,\n                                        className: \"w-8 h-8 sm:w-10 sm:h-10 bg-slate-800 rounded-xl flex items-center justify-center hover:bg-gradient-to-r hover:from-emerald-600 hover:to-blue-600 transition-all duration-300 transform hover:scale-110\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(social.icon, {\n                                            className: \"h-4 w-4 sm:h-5 sm:w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 816,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 811,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 804,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-slate-800 pt-6 w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-400 text-xs sm:text-sm\",\n                                        children: \"\\xa9 2024 ThePaperBull. All rights reserved.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 822,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row justify-center items-center gap-2 sm:gap-4 mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-emerald-400 transition-colors text-xs sm:text-sm\",\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 826,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline text-slate-600\",\n                                                children: \"|\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 827,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"hover:text-emerald-400 transition-colors text-xs sm:text-sm\",\n                                                children: \"Terms of Service\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 828,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 825,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 821,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 789,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 788,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 787,\n                columnNumber: 7\n            }, this),\n            showSignInModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-slate-800 rounded-2xl p-6 sm:p-8 w-full max-w-md relative max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowSignInModal(false),\n                            className: \"absolute top-4 right-4 text-slate-400 hover:text-slate-600 dark:hover:text-slate-200 z-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                className: \"h-5 w-5 sm:h-6 sm:w-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 843,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 839,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-6 sm:mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-emerald-600 to-blue-600 rounded-xl p-2 sm:p-3 w-fit mx-auto mb-3 sm:mb-4 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-6 w-6 sm:h-8 sm:w-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 848,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 847,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl sm:text-2xl font-bold text-slate-900 dark:text-white\",\n                                    children: \"Welcome Back, Trader\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 850,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm sm:text-base text-slate-600 dark:text-slate-300\",\n                                    children: \"Continue your trading journey with ThePaperBull\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 851,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 846,\n                            columnNumber: 13\n                        }, this),\n                        successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-emerald-50 dark:bg-emerald-900/20 border border-emerald-200 dark:border-emerald-800 text-emerald-800 dark:text-emerald-300 p-4 rounded-lg mb-6 text-sm\",\n                            children: successMessage\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 855,\n                            columnNumber: 15\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 p-4 rounded-lg mb-6 text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 861,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleLogin,\n                            className: \"space-y-4 sm:space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-xs sm:text-sm font-medium text-slate-700 dark:text-slate-300 mb-1 sm:mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 868,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            value: username,\n                                            onChange: (e)=>setUsername(e.target.value),\n                                            className: \"w-full px-3 sm:px-4 py-2 sm:py-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-slate-700 dark:text-white text-sm sm:text-base\",\n                                            placeholder: \"Enter your email\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 871,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 867,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-xs sm:text-sm font-medium text-slate-700 dark:text-slate-300 mb-1 sm:mb-2\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 882,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    value: password,\n                                                    onChange: (e)=>setPassword(e.target.value),\n                                                    className: \"w-full px-3 sm:px-4 py-2 sm:py-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-slate-700 dark:text-white pr-10 sm:pr-12 text-sm sm:text-base\",\n                                                    placeholder: \"Enter your password\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 886,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                    className: \"absolute right-2 sm:right-3 top-2 sm:top-3 text-slate-400 hover:text-slate-600 dark:hover:text-slate-200\",\n                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                        className: \"h-4 w-4 sm:h-5 sm:w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 899,\n                                                        columnNumber: 37\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                        className: \"h-4 w-4 sm:h-5 sm:w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 899,\n                                                        columnNumber: 84\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 894,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 885,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 881,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    checked: rememberMe,\n                                                    onChange: (e)=>setRememberMe(e.target.checked),\n                                                    className: \"rounded border-slate-300 text-emerald-600 focus:ring-emerald-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 906,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 text-xs sm:text-sm text-slate-600 dark:text-slate-300\",\n                                                    children: \"Remember me\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 912,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 905,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"text-xs sm:text-sm text-emerald-600 hover:text-emerald-500 text-left sm:text-right\",\n                                            children: \"Forgot password?\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 914,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 904,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading,\n                                    className: \"w-full bg-gradient-to-r from-emerald-600 to-blue-600 text-white py-2.5 sm:py-3 rounded-lg font-semibold hover:from-emerald-700 hover:to-blue-700 transition-all duration-200 disabled:opacity-50 text-sm sm:text-base\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 sm:h-5 sm:w-5 border-b-2 border-white mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 926,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Signing in...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 925,\n                                        columnNumber: 19\n                                    }, this) : \"Sign In\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 919,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 866,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 sm:mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full border-t border-slate-300 dark:border-slate-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 938,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 937,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex justify-center text-xs sm:text-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 bg-white dark:bg-slate-800 text-slate-500\",\n                                                children: \"Or continue with\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 941,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 940,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 936,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleGoogleAuth,\n                                    disabled: isLoading,\n                                    className: \"w-full mt-3 sm:mt-4 flex items-center justify-center bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-200 py-2.5 sm:py-3 rounded-lg font-semibold border border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-600 transition-all duration-200 text-sm sm:text-base\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-4 w-4 sm:h-5 sm:w-5 mr-2 sm:mr-3\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#4285F4\",\n                                                    d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 951,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#34A853\",\n                                                    d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 952,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#FBBC05\",\n                                                    d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 953,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#EA4335\",\n                                                    d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 954,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 950,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Continue with Google\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 945,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 935,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 sm:mt-6 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs sm:text-sm text-slate-600 dark:text-slate-300\",\n                                children: [\n                                    \"Don't have an account?\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowSignInModal(false);\n                                            setShowSignUpModal(true);\n                                        },\n                                        className: \"text-emerald-600 hover:text-emerald-500 font-medium\",\n                                        children: \"Sign up\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 963,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 961,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 960,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 838,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 837,\n                columnNumber: 9\n            }, this),\n            showSignUpModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-slate-800 rounded-2xl p-6 sm:p-8 w-full max-w-md relative max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowSignUpModal(false),\n                            className: \"absolute top-4 right-4 text-slate-400 hover:text-slate-600 dark:hover:text-slate-200 z-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                className: \"h-5 w-5 sm:h-6 sm:w-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 988,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 984,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-6 sm:mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-emerald-600 to-blue-600 rounded-xl p-2 sm:p-3 w-fit mx-auto mb-3 sm:mb-4 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_CheckCircle_DollarSign_Eye_EyeOff_Github_Globe_Instagram_Linkedin_Lock_Menu_PieChart_Quote_Rocket_Shield_Sparkles_Star_TrendingUp_Twitter_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-6 w-6 sm:h-8 sm:w-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 993,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 992,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl sm:text-2xl font-bold text-slate-900 dark:text-white\",\n                                    children: \"Join ThePaperBull\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 995,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm sm:text-base text-slate-600 dark:text-slate-300\",\n                                    children: \"Start mastering crypto futures with $10,000 virtual capital\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 996,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 991,\n                            columnNumber: 13\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 p-4 rounded-lg mb-6 text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 1000,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSignUp,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                    children: \"First Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 1008,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: signUpData.firstName,\n                                                    onChange: (e)=>setSignUpData({\n                                                            ...signUpData,\n                                                            firstName: e.target.value\n                                                        }),\n                                                    className: \"w-full px-4 py-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-slate-700 dark:text-white\",\n                                                    placeholder: \"John\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 1011,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1007,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                    children: \"Last Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 1021,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: signUpData.lastName,\n                                                    onChange: (e)=>setSignUpData({\n                                                            ...signUpData,\n                                                            lastName: e.target.value\n                                                        }),\n                                                    className: \"w-full px-4 py-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-slate-700 dark:text-white\",\n                                                    placeholder: \"Doe\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 1024,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1020,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 1006,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1036,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            value: signUpData.email,\n                                            onChange: (e)=>setSignUpData({\n                                                    ...signUpData,\n                                                    email: e.target.value\n                                                }),\n                                            className: \"w-full px-4 py-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-slate-700 dark:text-white\",\n                                            placeholder: \"<EMAIL>\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1039,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 1035,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1050,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"password\",\n                                            value: signUpData.password,\n                                            onChange: (e)=>setSignUpData({\n                                                    ...signUpData,\n                                                    password: e.target.value\n                                                }),\n                                            className: \"w-full px-4 py-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-slate-700 dark:text-white\",\n                                            placeholder: \"Create a strong password\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1053,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 1049,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                            children: \"Confirm Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1064,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"password\",\n                                            value: signUpData.confirmPassword,\n                                            onChange: (e)=>setSignUpData({\n                                                    ...signUpData,\n                                                    confirmPassword: e.target.value\n                                                }),\n                                            className: \"w-full px-4 py-3 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-slate-700 dark:text-white\",\n                                            placeholder: \"Confirm your password\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1067,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 1063,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            required: true,\n                                            className: \"rounded border-slate-300 text-emerald-600 focus:ring-emerald-500 mt-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1078,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 text-sm text-slate-600 dark:text-slate-300\",\n                                            children: [\n                                                \"I agree to the\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-emerald-600 hover:text-emerald-500\",\n                                                    children: \"Terms of Service\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 1085,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                \"and\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-emerald-600 hover:text-emerald-500\",\n                                                    children: \"Privacy Policy\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 1087,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1083,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 1077,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading,\n                                    className: \"w-full bg-gradient-to-r from-emerald-600 to-blue-600 text-white py-3 rounded-lg font-semibold hover:from-emerald-700 hover:to-blue-700 transition-all duration-200 disabled:opacity-50\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 1098,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Creating account...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 1097,\n                                        columnNumber: 19\n                                    }, this) : \"Create Account\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 1091,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 1005,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full border-t border-slate-300 dark:border-slate-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 1110,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1109,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex justify-center text-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 bg-white dark:bg-slate-800 text-slate-500\",\n                                                children: \"Or continue with\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 1113,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1112,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 1108,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleGoogleAuth,\n                                    disabled: isLoading,\n                                    className: \"w-full mt-4 flex items-center justify-center bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-200 py-3 rounded-lg font-semibold border border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-600 transition-all duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 mr-3\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#4285F4\",\n                                                    d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 1123,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#34A853\",\n                                                    d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 1124,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#FBBC05\",\n                                                    d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 1125,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"#EA4335\",\n                                                    d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 1126,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 1122,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Continue with Google\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 1117,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 1107,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-slate-600 dark:text-slate-300\",\n                                children: [\n                                    \"Already have an account?\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowSignUpModal(false);\n                                            setShowSignInModal(true);\n                                        },\n                                        className: \"text-emerald-600 hover:text-emerald-500 font-medium\",\n                                        children: \"Sign in\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 1135,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 1133,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 1132,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 983,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 982,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 229,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"tFozpZAlNjbgJ/Oxct37qqbIr64=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _components_ui_notification__WEBPACK_IMPORTED_MODULE_5__.useNotifications\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/login/page.tsx\n"));

/***/ })

});