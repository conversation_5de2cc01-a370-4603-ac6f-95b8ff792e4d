import { firestoreService } from './firestore-service'
import userService from './user-service'

interface UserPreferences {
  favoriteMarkets: string[]
  theme: 'light' | 'dark' | 'system'
  colorScheme: string
  rememberedUsername?: string
}

class UserPreferencesService {
  private preferences: UserPreferences = {
    favoriteMarkets: [],
    theme: 'light',
    colorScheme: 'emerald'
  }

  private subscribers: ((preferences: UserPreferences) => void)[] = []
  private isInitialized = false

  constructor() {
    // Initialize when user service is ready
    userService.subscribe((user) => {
      if (user) {
        this.loadUserPreferences(user.id)
      } else {
        this.resetPreferences()
      }
    })
  }

  // Subscribe to preference changes
  subscribe(callback: (preferences: UserPreferences) => void): () => void {
    this.subscribers.push(callback)
    
    // Immediately call with current preferences if initialized
    if (this.isInitialized) {
      callback(this.preferences)
    }

    return () => {
      const index = this.subscribers.indexOf(callback)
      if (index > -1) {
        this.subscribers.splice(index, 1)
      }
    }
  }

  private notifySubscribers() {
    this.subscribers.forEach(callback => callback(this.preferences))
  }

  private async loadUserPreferences(userId: string) {
    try {
      const userDoc = await firestoreService.getUserProfile(userId)
      if (userDoc?.preferences) {
        this.preferences = {
          ...this.preferences,
          ...userDoc.preferences
        }
      }
      this.isInitialized = true
      this.notifySubscribers()
    } catch (error) {
      console.error('Error loading user preferences:', error)
      this.isInitialized = true
      this.notifySubscribers()
    }
  }

  private resetPreferences() {
    this.preferences = {
      favoriteMarkets: [],
      theme: 'light',
      colorScheme: 'emerald'
    }
    this.isInitialized = false
  }

  // Get current preferences
  getPreferences(): UserPreferences {
    return { ...this.preferences }
  }

  // Update favorite markets
  async updateFavoriteMarkets(favoriteMarkets: string[]): Promise<void> {
    const user = userService.getFirebaseUser()
    if (!user) return

    try {
      this.preferences.favoriteMarkets = favoriteMarkets
      await firestoreService.updateUserProfile(user.uid, {
        preferences: this.preferences
      })
      this.notifySubscribers()
    } catch (error) {
      console.error('Error updating favorite markets:', error)
    }
  }

  // Update theme
  async updateTheme(theme: 'light' | 'dark' | 'system'): Promise<void> {
    const user = userService.getFirebaseUser()
    if (!user) {
      // Store locally if not authenticated
      this.preferences.theme = theme
      this.notifySubscribers()
      return
    }

    try {
      this.preferences.theme = theme
      await firestoreService.updateUserProfile(user.uid, {
        preferences: this.preferences
      })
      this.notifySubscribers()
    } catch (error) {
      console.error('Error updating theme:', error)
    }
  }

  // Update color scheme
  async updateColorScheme(colorScheme: string): Promise<void> {
    const user = userService.getFirebaseUser()
    if (!user) {
      // Store locally if not authenticated
      this.preferences.colorScheme = colorScheme
      this.notifySubscribers()
      return
    }

    try {
      this.preferences.colorScheme = colorScheme
      await firestoreService.updateUserProfile(user.uid, {
        preferences: this.preferences
      })
      this.notifySubscribers()
    } catch (error) {
      console.error('Error updating color scheme:', error)
    }
  }

  // Update remembered username
  async updateRememberedUsername(username: string | null): Promise<void> {
    try {
      if (username) {
        this.preferences.rememberedUsername = username
      } else {
        delete this.preferences.rememberedUsername
      }
      
      // For remembered username, we can store locally since it's used before authentication
      if (typeof window !== 'undefined') {
        if (username) {
          localStorage.setItem('rememberedUsername', username)
        } else {
          localStorage.removeItem('rememberedUsername')
        }
      }
      
      this.notifySubscribers()
    } catch (error) {
      console.error('Error updating remembered username:', error)
    }
  }

  // Get remembered username (fallback to localStorage for compatibility)
  getRememberedUsername(): string | null {
    if (this.preferences.rememberedUsername) {
      return this.preferences.rememberedUsername
    }
    
    if (typeof window !== 'undefined') {
      return localStorage.getItem('rememberedUsername')
    }
    
    return null
  }

  // Toggle favorite market
  async toggleFavoriteMarket(symbol: string): Promise<void> {
    const currentFavorites = this.preferences.favoriteMarkets
    const newFavorites = currentFavorites.includes(symbol)
      ? currentFavorites.filter(s => s !== symbol)
      : [...currentFavorites, symbol]
    
    await this.updateFavoriteMarkets(newFavorites)
  }

  // Check if market is favorite
  isFavoriteMarket(symbol: string): boolean {
    return this.preferences.favoriteMarkets.includes(symbol)
  }

  // Get favorite markets
  getFavoriteMarkets(): string[] {
    return [...this.preferences.favoriteMarkets]
  }

  // Get theme
  getTheme(): 'light' | 'dark' | 'system' {
    return this.preferences.theme
  }

  // Get color scheme
  getColorScheme(): string {
    return this.preferences.colorScheme
  }
}

// Export singleton instance
export const userPreferencesService = new UserPreferencesService()
export default userPreferencesService
