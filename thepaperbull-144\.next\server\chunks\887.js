exports.id=887,exports.ids=[887],exports.modules={38004:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i}),r(37413);var s=r(31658);let a={runtime:"edge",dynamic:"force-static",alt:"Cryptocurrency Trading Dashboard",size:{width:1200,height:630},contentType:"image/png"};async function i(e){let{__metadata_id__:t,...r}=await e.params,i=(0,s.fillMetadataSegment)(".",r,"opengraph-image"),{generateImageMetadata:o}=a;function n(e,t){let r={alt:e.alt,type:e.contentType||"image/png",url:i+(t?"/"+t:"")+"?53e13af1384a38e6"},{size:s}=e;return s&&(r.width=s.width,r.height=s.height),r}return o?(await o({params:r})).map((e,t)=>{let r=(e.id||t)+"";return n(e,r)}):[n(a,"")]}},47155:(e,t,r)=>{"use strict";r.d(t,{rM:()=>h,fx:()=>x});var s=r(60687),a=r(43210),i=r(90555),o=r(80043),n=r(21643),l=r(70695);class d{constructor(){this.state={positions:[],orders:[],trades:[],accountInfo:null,marketData:{},isLoading:!1,error:null},this.subscribers=[],this.userId=null,this.unsubscribeFunctions=[],this.lastOrderTime=0,this.ORDER_COOLDOWN=1e3,this.updateTimeout=null,n.A.subscribe(e=>{console.log("Realtime Trading Service - User state changed:",{hasUser:!!e,userId:e?.id,userStructure:e?Object.keys(e):null}),e&&e.id?this.userId!==e.id&&(console.log("Initializing realtime trading service for user:",e.id),this.initializeForUser(e.id)):(console.log("User logged out, cleaning up realtime trading service"),this.cleanup())});let e=n.A.getUser();e&&e.id&&!this.userId&&(console.log("Found existing user on startup, initializing:",e.id),this.initializeForUser(e.id))}subscribe(e){return this.subscribers.push(e),e(this.state),()=>{let t=this.subscribers.indexOf(e);t>-1&&this.subscribers.splice(t,1)}}notifySubscribers(){this.subscribers.forEach(e=>e(this.state))}notifySubscribersDebounced(){this.updateTimeout&&clearTimeout(this.updateTimeout),this.updateTimeout=setTimeout(()=>{this.notifySubscribers()},100)}async initializeForUser(e){this.userId=e,this.state.isLoading=!0,this.notifySubscribers();try{await this.initializeAccountInfo(),this.setupRealtimeListeners(),this.state.isLoading=!1,this.state.error=null,this.notifySubscribers(),console.log("Realtime trading service initialized for user:",e)}catch(e){console.error("Error initializing realtime trading service:",e),this.state.isLoading=!1,this.state.error="Failed to initialize trading service",this.notifySubscribers()}}async initializeAccountInfo(){if(!this.userId)return;let e=n.A.getUserBalance();this.state.accountInfo={totalWalletBalance:e,totalUnrealizedProfit:0,totalMarginBalance:e,totalPositionInitialMargin:0,totalOpenOrderInitialMargin:0,availableBalance:e,maxWithdrawAmount:e,updateTime:Date.now()}}setupRealtimeListeners(){if(!this.userId)return;let e=(0,i.KR)(o.Ye,`users/${this.userId}/positions`),t=(0,i.Zy)(e,e=>{let t=e.val();this.state.positions=t?Object.entries(t).map(([e,t])=>({id:e,...t,timestamp:t.timestamp||Date.now()})):[],this.updateAccountInfo(),this.notifySubscribers()}),r=(0,i.KR)(o.Ye,`users/${this.userId}/orders`),s=(0,i.Zy)(r,e=>{let t=e.val();this.state.orders=t?Object.entries(t).map(([e,t])=>({id:e,...t,timestamp:t.timestamp||Date.now()})):[],this.updateAccountInfo(),this.notifySubscribers()}),a=(0,i.KR)(o.Ye,`users/${this.userId}/trades`),n=(0,i.Zy)(a,e=>{let t=e.val();this.state.trades=t?Object.entries(t).map(([e,t])=>({id:e,...t,timestamp:t.timestamp||Date.now()})).sort((e,t)=>t.timestamp-e.timestamp):[],this.notifySubscribers()});this.unsubscribeFunctions=[()=>(0,i.AU)(e,"value",t),()=>(0,i.AU)(r,"value",s),()=>(0,i.AU)(a,"value",n)]}updateAccountInfo(){if(!this.state.accountInfo)return;let e=n.A.getUserBalance(),t=this.state.positions.reduce((e,t)=>e+(t.pnl||0),0),r=this.state.positions.reduce((e,t)=>e+(t.margin||0),0),s=this.state.orders.filter(e=>"NEW"===e.status).reduce((e,t)=>{let r=this.state.marketData[t.symbol]?.price||t.price;return e+t.origQty*r/(t.leverage||10)},0),a=r+s,i=Math.max(0,e-a);this.state.accountInfo.totalWalletBalance=e,this.state.accountInfo.totalUnrealizedProfit=t,this.state.accountInfo.totalPositionInitialMargin=r,this.state.accountInfo.totalOpenOrderInitialMargin=s,this.state.accountInfo.totalMarginBalance=e+t,this.state.accountInfo.availableBalance=i,this.state.accountInfo.maxWithdrawAmount=i,this.state.accountInfo.updateTime=Date.now(),console.log("Account Info Updated:",{userBalance:e,totalMargin:r,totalOrderMargin:s,totalUsedMargin:a,availableBalance:i,positionsCount:this.state.positions.length,ordersCount:this.state.orders.filter(e=>"NEW"===e.status).length})}updateMarketData(e,t){this.state.marketData[e]={...this.state.marketData[e],...t},t.price&&(this.state.positions=this.state.positions.map(r=>r.symbol===e?this.updatePositionPnL(r,t.price):r),this.updateAccountInfo(),this.notifySubscribersDebounced())}calculatePnL(e,t){let r=("LONG"===e.side?t-e.entryPrice:e.entryPrice-t)*e.size,s=e.margin>0?r/e.margin*100:0;return{pnl:Number(r.toFixed(2)),pnlPercent:Number(s.toFixed(2))}}updatePositionPnL(e,t){let{pnl:r,pnlPercent:s}=this.calculatePnL(e,t);return{...e,markPrice:t,pnl:r,pnlPercent:s}}calculateLiquidationPrice(e,t,r){let s=.995-1/r;return"LONG"===t?e*s:e*(2-s)}async placeOrder(e){if(!this.userId){let e=n.A.getFirebaseUser(),t=n.A.getUser();if(console.log("Authentication check in placeOrder:",{realtimeServiceUserId:this.userId,firebaseUser:e?{uid:e.uid,email:e.email}:null,userServiceUser:t?{id:t.id,email:t.email}:null}),e)console.log("Using Firebase user ID as fallback:",e.uid),this.userId=e.uid,await this.initializeForUser(e.uid);else if(t&&t.id)console.log("Using user service ID as fallback:",t.id),this.userId=t.id,await this.initializeForUser(t.id);else throw console.error("Authentication failed - no user ID available"),Error("User not authenticated. Please sign in and try again.")}let t=Date.now();if(t-this.lastOrderTime<this.ORDER_COOLDOWN){let e=this.ORDER_COOLDOWN-(t-this.lastOrderTime);throw Error(`Please wait ${Math.ceil(e/1e3)} second(s) before placing another order`)}let r=this.state.marketData[e.symbol]?.price||e.price||0;if(r<=0)throw Error("Invalid market price. Please try again.");let s=e.quantity*r,a=s/(e.leverage||10),d=.001*s,c=n.A.getUserBalance(),u=this.state.positions.reduce((e,t)=>e+(t.margin||0),0),m=this.state.orders.filter(e=>"NEW"===e.status).reduce((e,t)=>{let r=this.state.marketData[t.symbol]?.price||t.price;return e+t.origQty*r/(t.leverage||10)},0),h=u+m,x=c-h,p=a+d;if(console.log("Balance Validation:",{userBalance:c,currentMargin:u,pendingOrderMargin:m,totalUsedMargin:h,availableBalance:x,requiredMargin:a,commission:d,totalRequired:p,orderValue:s,leverage:e.leverage||10}),p>x)throw Error(`Insufficient balance. Required: ${p.toFixed(2)} USDT, Available: ${x.toFixed(2)} USDT`);if(x<10)throw Error("Insufficient balance. Minimum 10 USDT required for trading.");let b=`ord_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,g={id:b,symbol:e.symbol,side:e.side,type:e.type,origQty:e.quantity,executedQty:0,price:e.price||r,status:"NEW",timestamp:Date.now(),leverage:e.leverage||10};this.state.orders.push(g),this.updateAccountInfo(),this.notifySubscribers(),this.lastOrderTime=t;try{let t=(0,i.KR)(o.Ye,`users/${this.userId}/orders/${b}`);return await (0,i.hZ)(t,{...g,createdAt:(0,i.O5)()}),"MARKET"===e.type&&await this.executeOrder(b,g,r),l.l.createTradeNotification(this.userId,"order_placed",{symbol:e.symbol,side:e.side,type:e.type,price:e.price||r,quantity:e.quantity}),b}catch(e){throw this.state.orders=this.state.orders.filter(e=>e.id!==b),this.updateAccountInfo(),this.notifySubscribers(),e}}async executeOrder(e,t,r){if(!this.userId)return;let s=t.origQty*r*.001,a=t.origQty*r/t.leverage,d=n.A.getUser()?.balance?.current||n.A.getUserBalance(),c=this.state.positions.reduce((e,t)=>e+(t.margin||0),0),u=d-c,m=a+s;if(console.log("Execution Balance Check:",{userBalance:d,currentMargin:c,availableBalance:u,margin:a,commission:s,totalRequired:m,orderId:e}),m>u)throw this.state.orders=this.state.orders.filter(t=>t.id!==e),this.updateAccountInfo(),this.notifySubscribers(),Error(`Execution failed: Insufficient balance. Required: ${m.toFixed(2)} USDT, Available: ${u.toFixed(2)} USDT`);let h=`pos_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,x={id:h,symbol:t.symbol,side:"BUY"===t.side?"LONG":"SHORT",entryPrice:r,markPrice:r,size:t.origQty,margin:a,leverage:t.leverage,pnl:0,pnlPercent:0,liquidationPrice:this.calculateLiquidationPrice(r,"BUY"===t.side?"LONG":"SHORT",t.leverage),timestamp:Date.now(),orderId:e};this.state.positions.push(x);let p=this.state.orders.findIndex(t=>t.id===e);-1!==p&&(this.state.orders[p].status="FILLED",this.state.orders[p].executedQty=t.origQty),this.updateAccountInfo(),this.notifySubscribers(),l.l.createTradeNotification(this.userId,"position_opened",{symbol:t.symbol,side:x.side,size:t.origQty,entryPrice:r});try{let t=(0,i.KR)(o.Ye,`users/${this.userId}/positions/${h}`),r=(0,i.KR)(o.Ye,`users/${this.userId}/orders/${e}`);await Promise.all([(0,i.hZ)(t,{...x,createdAt:(0,i.O5)()}),(0,i.hZ)(r,{...this.state.orders[p],updatedAt:(0,i.O5)()})]);let a=n.A.getUserBalance();await n.A.updateBalance(a-s,"commission",`Trading commission: ${s.toFixed(2)} USDT`)}catch(e){console.error("Error saving position to Firebase:",e)}}async closePosition(e){if(!this.userId)return;let t=this.state.positions.findIndex(t=>t.id===e);if(-1===t)return;let r=this.state.positions[t],s=this.state.marketData[r.symbol]?.price||r.markPrice,a=r.size*s*.001;this.state.positions.splice(t,1),this.updateAccountInfo(),this.notifySubscribers(),l.l.createTradeNotification(this.userId,"position_closed",{symbol:r.symbol,side:r.side,pnl:r.pnl,closePrice:s});try{let t=`trade_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,l={id:t,symbol:r.symbol,side:"LONG"===r.side?"SELL":"BUY",price:s,quantity:r.size,commission:a,realizedPnl:r.pnl,timestamp:Date.now(),leverage:r.leverage,orderId:r.orderId||"",positionId:e};this.state.trades.unshift(l),this.notifySubscribers();let d=(0,i.KR)(o.Ye,`users/${this.userId}/positions/${e}`),c=(0,i.KR)(o.Ye,`users/${this.userId}/trades/${t}`);await Promise.all([(0,i.TF)(d),(0,i.hZ)(c,{...l,createdAt:(0,i.O5)()})]);let u=n.A.getUserBalance();await n.A.updateBalance(u+r.pnl-a,r.pnl>0?"trade_profit":"trade_loss",`Position closed: ${r.pnl>0?"+":""}${r.pnl.toFixed(2)} USDT`)}catch(e){throw console.error("Error closing position in Firebase:",e),this.state.positions.push(r),this.updateAccountInfo(),this.notifySubscribers(),e}}async cancelOrder(e){if(!this.userId)throw Error("User not authenticated");try{await (0,i.TF)((0,i.KR)(o.Ye,`users/${this.userId}/orders/${e}`)),this.state.orders=this.state.orders.filter(t=>t.id!==e),this.notifySubscribers(),console.log("Order canceled successfully:",e)}catch(e){throw console.error("Error canceling order:",e),e}}getState(){return{...this.state}}getMarketData(e){return this.state.marketData[e]||null}cleanup(){this.unsubscribeFunctions.forEach(e=>e()),this.unsubscribeFunctions=[],this.updateTimeout&&(clearTimeout(this.updateTimeout),this.updateTimeout=null),this.userId=null,this.state={positions:[],orders:[],trades:[],accountInfo:null,marketData:{},isLoading:!1,error:null},this.notifySubscribers()}}let c=new d;class u{constructor(){this.simulators=new Map,this.intervalId=null,this.subscribers=[],this.initializeSimulators()}initializeSimulators(){[{symbol:"BTCUSDT",basePrice:43200,volatility:.02},{symbol:"ETHUSDT",basePrice:2320,volatility:.025},{symbol:"SOLUSDT",basePrice:142,volatility:.03},{symbol:"ADAUSDT",basePrice:.45,volatility:.035},{symbol:"XRPUSDT",basePrice:.62,volatility:.04},{symbol:"BNBUSDT",basePrice:315,volatility:.025},{symbol:"DOGEUSDT",basePrice:.08,volatility:.05},{symbol:"TRXUSDT",basePrice:.12,volatility:.04},{symbol:"LINKUSDT",basePrice:14.5,volatility:.03},{symbol:"AVAXUSDT",basePrice:38.2,volatility:.035}].forEach(e=>{this.simulators.set(e.symbol,{symbol:e.symbol,basePrice:e.basePrice,volatility:e.volatility,trend:(Math.random()-.5)*.001,lastPrice:e.basePrice,lastUpdate:Date.now()})})}start(){this.intervalId||(this.intervalId=setInterval(()=>{this.updatePrices()},2e3))}stop(){this.intervalId&&(clearInterval(this.intervalId),this.intervalId=null)}updatePrices(){this.simulators.forEach(e=>{let t=Date.now(),r=(t-e.lastUpdate)/1e3,s=(Math.random()-.5)*e.volatility*r,a=e.trend*r,i=e.lastPrice*(1+(s+a)),o=i-e.lastPrice,n=o/e.lastPrice*100;.01>Math.random()&&(e.trend=(Math.random()-.5)*.001),e.lastPrice=i,e.lastUpdate=t;let l={symbol:e.symbol,price:i,priceChange:o,priceChangePercent:n,volume:1e6*Math.random(),timestamp:t};this.notifySubscribers(e.symbol,l)})}subscribe(e){return this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);t>-1&&this.subscribers.splice(t,1)}}notifySubscribers(e,t){this.subscribers.forEach(r=>r(e,t))}getCurrentPrice(e){let t=this.simulators.get(e);return t?t.lastPrice:null}addSymbol(e,t,r=.03){this.simulators.has(e)||this.simulators.set(e,{symbol:e,basePrice:t,volatility:r,trend:(Math.random()-.5)*.001,lastPrice:t,lastUpdate:Date.now()})}removeSymbol(e){this.simulators.delete(e)}}new u;let m=(0,a.createContext)(void 0);function h({children:e}){let[t,r]=(0,a.useState)({positions:[],orders:[],trades:[],marketData:{},accountInfo:null,isLoading:!1,error:null}),i=(0,a.useCallback)(async e=>{try{return await c.placeOrder(e)}catch(e){throw console.error("Failed to place order:",e),e}},[]),o=(0,a.useCallback)(async e=>{try{return console.log("Cancel order not implemented yet:",e),!0}catch(e){throw console.error("Failed to cancel order:",e),e}},[]),n=(0,a.useCallback)(async e=>{try{return await c.closePosition(e),!0}catch(e){throw console.error("Failed to close position:",e),e}},[]),l=(0,a.useCallback)(async e=>{try{return console.log("Position update not implemented in Firebase service yet:",e),!0}catch(e){throw console.error("Failed to update position:",e),e}},[]),d=(0,a.useCallback)((e,t)=>{c.updateMarketData(e,t)},[]),u=(0,a.useCallback)(e=>{console.log("Account info update not needed with Realtime service:",e)},[]),h=(0,a.useCallback)(()=>{r(e=>({...e,error:null}))},[]),x=(0,a.useCallback)(e=>c.getMarketData(e),[]),p=(0,a.useCallback)(e=>t.positions.find(t=>t.symbol===e)||null,[t.positions]),b=(0,a.useCallback)(()=>t.accountInfo,[t.accountInfo]),g=(0,a.useCallback)(()=>t.positions.reduce((e,t)=>e+t.pnl,0),[t.positions]),f=(0,a.useCallback)(()=>t.positions.reduce((e,t)=>e+t.margin,0),[t.positions]),y=(0,a.useCallback)(()=>t.accountInfo?.availableBalance||0,[t.accountInfo]),v={positions:t.positions,orders:t.orders,trades:t.trades,marketData:t.marketData,accountInfo:t.accountInfo,isLoading:t.isLoading,error:t.error,state:t,placeOrder:i,cancelOrder:o,closePosition:n,updatePosition:l,updateMarketData:d,updateAccountInfo:u,clearError:h,getMarketData:x,getPositionBySymbol:p,getAccountInfo:b,getTotalPnL:g,getTotalMargin:f,getAvailableBalance:y};return(0,s.jsx)(m.Provider,{value:v,children:e})}function x(){let e=(0,a.useContext)(m);if(void 0===e)throw Error("useTrading must be used within a TradingProvider");return e}},50579:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>B});var s=r(60687),a=r(43210),i=r(16189),o=r(85814),n=r.n(o),l=r(32192),d=r(20835),c=r(3589),u=r(78272),m=r(35583),h=r(53411),x=r(58887),p=r(84027),b=r(40083),g=r(25878),f=r(21134),y=r(363);function v(){let{theme:e,setTheme:t}=(0,g.D)();return(0,s.jsx)("button",{onClick:()=>t("dark"===e?"light":"dark"),className:"relative p-2.5 rounded-xl bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10 hover:from-primary/20 hover:via-primary/10 hover:to-primary/20 border border-primary/20 hover:border-primary/30 transition-all duration-300 group shadow-md hover:shadow-lg","aria-label":"dark"===e?"Switch to light theme":"Switch to dark theme",children:(0,s.jsx)("div",{className:"relative",children:"dark"===e?(0,s.jsx)(f.A,{className:"h-4 w-4 text-yellow-500 group-hover:scale-110 group-hover:rotate-180 transition-all duration-300"}):(0,s.jsx)(y.A,{className:"h-4 w-4 text-blue-600 dark:text-blue-400 group-hover:scale-110 group-hover:-rotate-12 transition-all duration-300"})})})}function w({onCloseMobile:e}){let t=(0,i.usePathname)(),[o,g]=(0,a.useState)("trading"),f=e=>{g(o===e?null:e)},y=()=>{e&&e()};return(0,s.jsxs)("div",{className:"h-full flex flex-col bg-gradient-to-br from-card via-card/98 to-muted/20 text-card-foreground backdrop-blur-md border-r border-border/30 shadow-2xl",children:[(0,s.jsx)("div",{className:"flex-1 overflow-auto py-responsive",children:(0,s.jsxs)("nav",{className:"px-responsive space-y-3",children:[(0,s.jsx)("div",{className:"px-2 mb-4",children:(0,s.jsx)("h3",{className:"text-2xs font-bold text-muted-foreground uppercase tracking-wider",children:"Navigation"})}),(0,s.jsx)("div",{children:(0,s.jsxs)(n(),{href:"/trade",className:`group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ${"/trade"===t?"bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105":"text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"}`,onClick:y,children:[(0,s.jsx)("div",{className:`mr-3 p-1.5 rounded-lg transition-all duration-300 ${"/trade"===t?"bg-primary-foreground/20":"group-hover:bg-primary/10"}`,children:(0,s.jsx)(l.A,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"font-medium",children:"Trade"}),"/trade"===t&&(0,s.jsx)("div",{className:"ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"})]})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("button",{className:"group w-full flex items-center justify-between px-4 py-3.5 text-sm rounded-2xl text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg transition-all duration-300",onClick:()=>f("trading"),children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"mr-3 p-1.5 rounded-lg group-hover:bg-primary/10 transition-all duration-300",children:(0,s.jsx)(d.A,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"font-medium",children:"Trading"})]}),"trading"===o?(0,s.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground transition-transform duration-300 group-hover:text-foreground"}):(0,s.jsx)(u.A,{className:"h-4 w-4 text-muted-foreground transition-transform duration-300 group-hover:text-foreground"})]}),"trading"===o&&(0,s.jsxs)("div",{className:"mt-3 ml-8 space-y-2 animate-in slide-in-from-top-3 duration-300",children:[(0,s.jsxs)(n(),{href:"/trade",className:`group flex items-center justify-between px-4 py-3 text-sm rounded-xl transition-all duration-300 ${"/trade"===t?"bg-gradient-to-r from-primary/30 via-primary/25 to-primary/20 text-primary font-bold border border-primary/40 shadow-lg shadow-primary/20 scale-105":"text-foreground hover:text-primary hover:bg-gradient-to-r hover:from-primary/10 hover:to-primary/5 hover:border hover:border-primary/20 hover:shadow-md hover:scale-102 font-semibold"}`,onClick:y,children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:`mr-3 p-1.5 rounded-lg transition-all duration-300 ${"/trade"===t?"bg-primary/20 text-primary":"bg-primary/10 text-primary group-hover:bg-primary/15"}`,children:(0,s.jsx)(d.A,{className:"h-3.5 w-3.5"})}),(0,s.jsx)("span",{children:"Futures"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:["/trade"===t&&(0,s.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full animate-pulse"}),(0,s.jsx)("span",{className:"text-xs bg-gradient-to-r from-emerald-500/20 to-emerald-600/20 text-emerald-600 dark:text-emerald-400 px-2.5 py-1 rounded-lg font-bold border border-emerald-500/30 shadow-sm",children:"LIVE"})]})]}),(0,s.jsxs)(n(),{href:"#",className:"group flex items-center justify-between px-4 py-2.5 text-sm rounded-xl text-muted-foreground hover:text-foreground hover:bg-gradient-to-r hover:from-muted/40 hover:to-muted/20 transition-all duration-300",onClick:y,children:[(0,s.jsx)("span",{children:"Margin"}),(0,s.jsx)("span",{className:"text-xs bg-gradient-to-r from-amber-500/20 to-amber-600/20 text-amber-600 dark:text-amber-400 px-2.5 py-1 rounded-lg font-medium border border-amber-500/20",children:"Soon"})]}),(0,s.jsxs)(n(),{href:"#",className:"group flex items-center justify-between px-4 py-2.5 text-sm rounded-xl text-muted-foreground hover:text-foreground hover:bg-gradient-to-r hover:from-muted/40 hover:to-muted/20 transition-all duration-300",onClick:y,children:[(0,s.jsx)("span",{children:"Options"}),(0,s.jsx)("span",{className:"text-xs bg-gradient-to-r from-amber-500/20 to-amber-600/20 text-amber-600 dark:text-amber-400 px-2.5 py-1 rounded-lg font-medium border border-amber-500/20",children:"Soon"})]})]})]}),(0,s.jsx)("div",{children:(0,s.jsxs)(n(),{href:"/wallet",className:`group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ${"/wallet"===t?"bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105":"text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"}`,onClick:y,children:[(0,s.jsx)("div",{className:`mr-3 p-1.5 rounded-lg transition-all duration-300 ${"/wallet"===t?"bg-primary-foreground/20":"group-hover:bg-primary/10"}`,children:(0,s.jsx)(m.A,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"font-medium",children:"Wallet"}),"/wallet"===t&&(0,s.jsx)("div",{className:"ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"})]})}),(0,s.jsx)("div",{children:(0,s.jsxs)(n(),{href:"/statistics",className:`group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ${"/statistics"===t?"bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105":"text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"}`,onClick:y,children:[(0,s.jsx)("div",{className:`mr-3 p-1.5 rounded-lg transition-all duration-300 ${"/statistics"===t?"bg-primary-foreground/20":"group-hover:bg-primary/10"}`,children:(0,s.jsx)(h.A,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"font-medium",children:"Statistics"}),"/statistics"===t&&(0,s.jsx)("div",{className:"ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"})]})}),(0,s.jsx)("div",{children:(0,s.jsxs)(n(),{href:"/chat",className:`group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ${"/chat"===t?"bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105":"text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"}`,onClick:y,children:[(0,s.jsx)("div",{className:`mr-3 p-1.5 rounded-lg transition-all duration-300 ${"/chat"===t?"bg-primary-foreground/20":"group-hover:bg-primary/10"}`,children:(0,s.jsx)(x.A,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"font-medium",children:"AI Chat"}),"/chat"===t&&(0,s.jsx)("div",{className:"ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"})]})}),(0,s.jsx)("div",{children:(0,s.jsxs)(n(),{href:"/settings",className:`group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ${"/settings"===t?"bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105":"text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"}`,onClick:y,children:[(0,s.jsx)("div",{className:`mr-3 p-1.5 rounded-lg transition-all duration-300 ${"/settings"===t?"bg-primary-foreground/20":"group-hover:bg-primary/10"}`,children:(0,s.jsx)(p.A,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"font-medium",children:"Settings"}),"/settings"===t&&(0,s.jsx)("div",{className:"ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"})]})})]})}),(0,s.jsx)("div",{className:"p-responsive border-t border-border/30 bg-gradient-to-br from-muted/30 via-muted/20 to-background/50 backdrop-blur-sm",children:(0,s.jsxs)("div",{className:"space-y-3 sm:space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-2xl bg-gradient-to-r from-background/60 to-muted/40 border border-border/30",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full animate-pulse"}),(0,s.jsx)("span",{className:"text-responsive-xs font-semibold text-foreground",children:"Theme"})]}),(0,s.jsx)(v,{})]}),(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("span",{className:"bg-gradient-to-r from-primary/25 via-primary/20 to-primary/15 text-primary px-3 py-1.5 sm:px-4 sm:py-2 rounded-2xl text-2xs sm:text-xs font-bold border border-primary/30 shadow-lg shadow-primary/10",children:"BETA VERSION"}),(0,s.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-emerald-400 to-emerald-500 rounded-full border-2 border-background animate-bounce"})]})}),(0,s.jsxs)("button",{onClick:async()=>{try{let{signOutUser:e}=await Promise.resolve().then(r.bind(r,95216));await e(),localStorage.removeItem("rememberedUsername"),window.location.href="/login"}catch(e){console.error("Logout error:",e),window.location.href="/login"}},className:"touch-target group flex items-center justify-center px-4 py-3.5 text-responsive-xs rounded-2xl bg-gradient-to-r from-red-500/15 via-red-500/10 to-red-600/15 text-red-600 dark:text-red-400 hover:from-red-500/25 hover:via-red-500/20 hover:to-red-600/25 border border-red-500/25 hover:border-red-500/40 transition-all duration-300 w-full shadow-lg hover:shadow-xl hover:scale-105",children:[(0,s.jsx)("div",{className:"mr-3 p-1 rounded-lg bg-red-500/20 group-hover:bg-red-500/30 transition-all duration-300",children:(0,s.jsx)(b.A,{className:"h-4 w-4 group-hover:scale-110 transition-transform duration-300"})}),(0,s.jsx)("span",{className:"font-semibold",children:"Logout"})]})]})})]})}var j=r(93613),N=r(86356),k=r(85778),I=r(65668),A=r(47155),S=r(6955);function D(){let[e,t]=(0,a.useState)(!1),[r,o]=(0,a.useState)(null),[n,l]=(0,a.useState)(!0),[d,c]=(0,a.useState)(null),u=(0,a.useRef)(null),h=(0,i.useRouter)(),{user:x,signOut:g}=(0,S.A)(),{accountInfo:f,getTotalPnL:y,getAvailableBalance:v}=(0,A.fx)(),w=(()=>{let e=r?.balance?.current||1e4,t=f?.totalWalletBalance||1e4,s=e||t||1e4,a=v();if(null==a||0===a){let e=y()||0;a=Math.max(0,s-(f?.totalPositionInitialMargin||0)+e)}return{totalBalance:s,availableBalance:a,totalPnL:y()||0,currency:r?.balance?.currency||"USDT"}})(),D=async()=>{try{await g(),h.push("/login")}catch(e){console.error("Sign out error:",e)}},P=(e="sm")=>n?(0,s.jsx)("div",{className:`${"sm"===e?"w-10 h-10":"w-16 h-16"} bg-muted rounded-full animate-pulse`}):(r?.avatar,(0,s.jsx)("div",{className:`${{sm:"w-10 h-10 text-sm",lg:"w-16 h-16 text-xl"}["sm"===e?"sm":"lg"]} rounded-full bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center shadow-md border-2 border-white dark:border-gray-700 transition-all duration-200 hover:shadow-lg`,children:(0,s.jsx)("span",{className:"select-none",children:"\uD83D\uDC02"})}));return(0,s.jsxs)("div",{className:"relative",ref:u,children:[(0,s.jsx)("button",{className:`rounded-full transition-all duration-200 ${e?"ring-2 ring-primary/30":"hover:scale-105"} ${n?"opacity-75":""}`,onClick:()=>t(!e),disabled:n,children:P("sm")}),e&&(0,s.jsxs)("div",{className:"absolute right-0 mt-2 w-56 rounded-md shadow-xl bg-card ring-1 ring-black/5 dark:ring-white/10 z-[100] animate-in fade-in slide-in-from-top-5 duration-200",children:[(0,s.jsxs)("div",{className:"py-2 px-3 border-b border-border",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[P("xl"),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:r?.name||x?.displayName||"User"}),r?.subscription?.type&&"free"!==r.subscription.type&&(0,s.jsx)("span",{className:"text-xs bg-primary/20 text-primary px-1.5 py-0.5 rounded-md uppercase",children:r.subscription.type})]}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:r?.email||x?.email||"<EMAIL>"})]})]}),d&&(0,s.jsx)("div",{className:"mt-3 pt-2 border-t border-border/50",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-red-500",children:[(0,s.jsx)(j.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"text-xs",children:d})]})}),!d&&(0,s.jsxs)("div",{className:"mt-3 pt-2 border-t border-border/50",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,s.jsx)("span",{className:"text-xs text-muted-foreground",children:"Total Balance"}),n?(0,s.jsx)("div",{className:"h-4 w-16 bg-muted rounded animate-pulse"}):(0,s.jsxs)("span",{className:"text-sm font-medium",children:[w.totalBalance.toFixed(2)," ",w.currency]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,s.jsx)("span",{className:"text-xs text-muted-foreground",children:"Available"}),n?(0,s.jsx)("div",{className:"h-4 w-16 bg-muted rounded animate-pulse"}):(0,s.jsxs)("span",{className:"text-sm font-medium",children:[w.availableBalance.toFixed(2)," ",w.currency]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-xs text-muted-foreground",children:"Unrealized PnL"}),n?(0,s.jsx)("div",{className:"h-4 w-16 bg-muted rounded animate-pulse"}):(0,s.jsxs)("span",{className:`text-sm font-medium ${w.totalPnL>=0?"text-emerald-500":"text-red-500"}`,children:[w.totalPnL>=0?"+":"",w.totalPnL.toFixed(2)," ",w.currency]})]}),r?.balance?.lastUpdated&&(0,s.jsx)("div",{className:"mt-2 pt-1 border-t border-border/30",children:(0,s.jsxs)("span",{className:"text-xs text-muted-foreground",children:["Updated: ",new Date(r.balance.lastUpdated).toLocaleTimeString()]})})]})]}),(0,s.jsxs)("div",{className:"py-1",children:[(0,s.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors duration-150",onClick:()=>{t(!1),h.push("/settings")},children:[(0,s.jsx)(N.A,{className:"h-4 w-4 mr-3 text-muted-foreground"}),"My Profile"]}),(0,s.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors duration-150",onClick:()=>{t(!1),h.push("/wallet")},children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-3 text-muted-foreground"}),"Wallet"]}),(0,s.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors duration-150",onClick:()=>{t(!1),h.push("/settings?tab=payment")},children:[(0,s.jsx)(k.A,{className:"h-4 w-4 mr-3 text-muted-foreground"}),"Billing"]}),(0,s.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors duration-150",onClick:()=>{t(!1),h.push("/settings")},children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-3 text-muted-foreground"}),"Settings"]}),(0,s.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors duration-150",onClick:()=>{t(!1),window.open("mailto:<EMAIL>","_blank")},children:[(0,s.jsx)(I.A,{className:"h-4 w-4 mr-3 text-muted-foreground"}),"Help Center"]})]}),(0,s.jsx)("div",{className:"py-1 border-t border-border",children:(0,s.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-muted transition-colors duration-150",onClick:D,children:[(0,s.jsx)(b.A,{className:"h-4 w-4 mr-3"}),"Sign out"]})})]})]})}r(21643);var P=r(11860),C=r(12941);function T({onToggle:e,isOpen:t}){return(0,s.jsx)("button",{className:"lg:hidden touch-target rounded-md hover:bg-muted transition-colors",onClick:()=>e(!t),"aria-label":t?"Close sidebar":"Open sidebar",children:t?(0,s.jsx)(P.A,{className:"h-6 w-6 text-foreground"}):(0,s.jsx)(C.A,{className:"h-6 w-6 text-foreground"})})}var U=r(25541),O=r(5336),$=r(43649),L=r(96882),E=r(97051),M=r(13964),R=r(70695),F=r(26512);function z({isOpen:e,onToggle:t,onClose:r}){let{user:i}=(0,S.A)(),[o,n]=(0,a.useState)([]),[l,d]=(0,a.useState)(0),c=(0,a.useRef)(null),u=async e=>{await R.A.markAsRead(e)},m=async()=>{await R.A.markAllAsRead()},h=e=>{switch(e){case"trade":return(0,s.jsx)(U.A,{className:"h-4 w-4 text-blue-500"});case"success":return(0,s.jsx)(O.A,{className:"h-4 w-4 text-green-500"});case"warning":return(0,s.jsx)($.A,{className:"h-4 w-4 text-yellow-500"});case"error":return(0,s.jsx)($.A,{className:"h-4 w-4 text-red-500"});default:return(0,s.jsx)(L.A,{className:"h-4 w-4 text-blue-500"})}},x=e=>{if(!e)return"Just now";try{let t=e.toDate?e.toDate():new Date(e);return(0,F.m)(t,{addSuffix:!0})}catch(e){return"Just now"}};return(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("button",{onClick:t,className:"touch-target relative text-muted-foreground hover:text-foreground transition-colors","aria-label":"Notifications",children:[(0,s.jsx)(E.A,{className:"h-5 w-5"}),l>0&&(0,s.jsx)("span",{className:"absolute -top-0.5 -right-0.5 bg-primary text-primary-foreground text-2xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse font-semibold",children:l>99?"99+":l})]}),e&&(0,s.jsxs)("div",{ref:c,className:"absolute right-0 mt-2 w-80 max-w-[calc(100vw-2rem)] bg-card rounded-lg shadow-xl border border-border z-[100] animate-in fade-in slide-in-from-top-5 duration-200",children:[(0,s.jsx)("div",{className:"p-3 border-b border-border",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h3",{className:"font-semibold text-responsive-sm",children:"Notifications"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[l>0&&(0,s.jsx)("button",{onClick:m,className:"text-2xs text-primary hover:text-primary/90 cursor-pointer font-medium",children:"Mark all as read"}),(0,s.jsx)("button",{onClick:r,className:"text-muted-foreground hover:text-foreground p-1 rounded-md hover:bg-muted transition-colors",children:(0,s.jsx)(P.A,{className:"h-4 w-4"})})]})]})}),(0,s.jsx)("div",{className:"max-h-[400px] overflow-y-auto",children:0===o.length?(0,s.jsxs)("div",{className:"p-6 text-center",children:[(0,s.jsx)(E.A,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-responsive-xs text-muted-foreground",children:"No notifications yet"}),(0,s.jsx)("p",{className:"text-2xs text-muted-foreground mt-1",children:"You'll see trading updates and system notifications here"})]}):(0,s.jsx)("div",{className:"divide-y divide-border",children:o.slice(0,10).map(e=>(0,s.jsx)("div",{className:`p-3 hover:bg-muted/50 transition-colors ${e.read?"":"bg-primary/5 border-l-2 border-l-primary"}`,children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:h(e.type)}),(0,s.jsx)("div",{className:"flex-1 min-w-0",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("p",{className:"text-responsive-xs font-medium text-foreground",children:e.title}),(0,s.jsx)("p",{className:"text-2xs text-muted-foreground mt-1 line-clamp-2",children:e.message}),(0,s.jsx)("p",{className:"text-2xs text-muted-foreground mt-1",children:x(e.createdAt)})]}),!e.read&&(0,s.jsx)("button",{onClick:()=>u(e.id),className:"ml-2 p-1 text-muted-foreground hover:text-foreground rounded-md hover:bg-muted transition-colors",title:"Mark as read",children:(0,s.jsx)(M.A,{className:"h-3 w-3"})})]})})]})},e.id))})}),o.length>0&&(0,s.jsx)("div",{className:"p-2 border-t border-border text-center",children:(0,s.jsx)("button",{className:"text-responsive-xs text-primary hover:text-primary/90 font-medium",children:"View all notifications"})})]})]})}function B({children:e}){(0,i.useRouter)();let[t,r]=(0,a.useState)(!0),[o,n]=(0,a.useState)(!1),[l,d]=(0,a.useState)(!1),[c,u]=(0,a.useState)(!0),[m,h]=(0,a.useState)(!1),{user:x,loading:p}=(0,S.A)(),b=(0,a.useRef)(null);return t||p?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("svg",{className:"animate-spin h-12 w-12 text-primary mx-auto",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,s.jsx)("p",{className:"mt-4 text-lg font-medium text-foreground",children:"Loading..."})]})}):(0,s.jsx)(A.rM,{children:(0,s.jsx)("div",{className:"min-h-screen w-full bg-background text-foreground",children:(0,s.jsx)("div",{className:"h-full w-full",children:(0,s.jsxs)("div",{className:"bg-card shadow-lg h-full",children:[(0,s.jsxs)("header",{className:"relative flex items-center justify-between px-4 py-2 border-b border-border bg-card/50 backdrop-blur-sm z-50",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"lg:hidden",children:(0,s.jsx)(T,{onToggle:n,isOpen:o})}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("h1",{className:"text-primary font-bold text-xl tracking-tight bg-gradient-to-r from-primary via-primary/90 to-primary/70 bg-clip-text text-transparent",children:"ThePaperBull"}),(0,s.jsx)("span",{className:"ml-2 text-xs bg-gradient-to-r from-primary/20 to-primary/30 text-primary px-2 py-0.5 rounded-full font-semibold border border-primary/20",children:"BETA"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3 sm:space-x-5 relative z-50",ref:b,children:[(0,s.jsx)(z,{isOpen:l,onToggle:()=>d(!l),onClose:()=>d(!1)}),(0,s.jsx)(D,{})]})]}),(0,s.jsxs)("div",{className:"relative h-[calc(100vh-60px)]",children:[o&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-20 md:hidden"}),(0,s.jsx)("div",{id:"sidebar",className:`${o?"translate-x-0":"-translate-x-full"} fixed inset-y-0 left-0 z-30 w-80 transform transition-all duration-300 ease-in-out md:translate-x-0 shadow-xl`,style:{top:"60px",height:"calc(100vh - 60px)",transform:m&&window.innerWidth>=768?`translateX(${c?"0":"-100%"})`:o?"translateX(0)":"translateX(-100%)"},children:(0,s.jsx)(w,{onCloseMobile:()=>n(!1)})}),(0,s.jsx)("div",{className:"hidden md:flex fixed left-0 top-1/2 transform -translate-y-1/2 z-40",children:(0,s.jsx)("button",{onClick:()=>u(!c),className:"bg-gradient-to-r from-primary to-primary/80 text-primary-foreground rounded-r-xl p-3 shadow-lg hover:shadow-xl transition-all duration-300 group",style:{transform:`translateX(${c?"320px":"0px"})`},"aria-label":c?"Hide sidebar":"Show sidebar",children:c?(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"group-hover:scale-110 transition-transform duration-200",children:(0,s.jsx)("path",{d:"m15 18-6-6 6-6"})}):(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"group-hover:scale-110 transition-transform duration-200",children:(0,s.jsx)("path",{d:"m9 18 6-6-6-6"})})})}),(0,s.jsx)("div",{className:"w-full h-full overflow-auto",children:(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)("div",{children:"Loading..."}),children:e})})]})]})})})})}},56777:(e,t,r)=>{Promise.resolve().then(r.bind(r,50579))},71934:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\(dashboard)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\step-by-step\\thepaperbull-144\\app\\(dashboard)\\layout.tsx","default")},75870:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i}),r(37413);var s=r(31658);let a={runtime:"edge",dynamic:"force-static",alt:"Cryptocurrency Trading Dashboard",size:{width:1200,height:630},contentType:"image/png"};async function i(e){let{__metadata_id__:t,...r}=await e.params,i=(0,s.fillMetadataSegment)(".",r,"twitter-image"),{generateImageMetadata:o}=a;function n(e,t){let r={alt:e.alt,type:e.contentType||"image/png",url:i+(t?"/"+t:"")+"?c783d68783a9fc8b"},{size:s}=e;return s&&(r.width=s.width,r.height=s.height),r}return o?(await o({params:r})).map((e,t)=>{let r=(e.id||t)+"";return n(e,r)}):[n(a,"")]}},91625:(e,t,r)=>{Promise.resolve().then(r.bind(r,71934))}};