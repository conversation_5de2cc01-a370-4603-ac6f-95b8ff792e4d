// Market data simulator for demo purposes
import type { MarketData } from '../types/trading'

interface PriceSimulator {
  symbol: string
  basePrice: number
  volatility: number
  trend: number
  lastPrice: number
  lastUpdate: number
}

class MarketDataSimulator {
  private simulators: Map<string, PriceSimulator> = new Map()
  private intervalId: NodeJS.Timeout | null = null
  private subscribers: ((symbol: string, data: MarketData) => void)[] = []

  constructor() {
    // Initialize simulators for common trading pairs
    this.initializeSimulators()
  }

  private initializeSimulators() {
    const pairs = [
      { symbol: 'BTCUSDT', basePrice: 43200, volatility: 0.02 },
      { symbol: 'ETHUSDT', basePrice: 2320, volatility: 0.025 },
      { symbol: 'SOLUSDT', basePrice: 142, volatility: 0.03 },
      { symbol: 'ADAUSDT', basePrice: 0.45, volatility: 0.035 },
      { symbol: 'XRPUSDT', basePrice: 0.62, volatility: 0.04 },
      { symbol: 'BNBUSDT', basePrice: 315, volatility: 0.025 },
      { symbol: 'DOGEUSDT', basePrice: 0.08, volatility: 0.05 },
      { symbol: 'TRXUSDT', basePrice: 0.12, volatility: 0.04 },
      { symbol: 'LINKUSDT', basePrice: 14.5, volatility: 0.03 },
      { symbol: 'AVAXUSDT', basePrice: 38.2, volatility: 0.035 }
    ]

    pairs.forEach(pair => {
      this.simulators.set(pair.symbol, {
        symbol: pair.symbol,
        basePrice: pair.basePrice,
        volatility: pair.volatility,
        trend: (Math.random() - 0.5) * 0.001, // Random trend between -0.05% and +0.05%
        lastPrice: pair.basePrice,
        lastUpdate: Date.now()
      })
    })
  }

  start() {
    if (this.intervalId) return

    this.intervalId = setInterval(() => {
      this.updatePrices()
    }, 2000) // Update every 2 seconds (reduced frequency for better performance)
  }

  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }
  }

  private updatePrices() {
    this.simulators.forEach((simulator) => {
      const now = Date.now()
      const timeDelta = (now - simulator.lastUpdate) / 1000 // seconds

      // Generate random price movement
      const randomChange = (Math.random() - 0.5) * simulator.volatility * timeDelta
      const trendChange = simulator.trend * timeDelta
      const totalChange = randomChange + trendChange

      // Update price
      const newPrice = simulator.lastPrice * (1 + totalChange)
      const priceChange = newPrice - simulator.lastPrice
      const priceChangePercent = (priceChange / simulator.lastPrice) * 100

      // Occasionally change trend
      if (Math.random() < 0.01) { // 1% chance per update
        simulator.trend = (Math.random() - 0.5) * 0.001
      }

      // Update simulator
      simulator.lastPrice = newPrice
      simulator.lastUpdate = now

      // Create market data
      const marketData: MarketData = {
        symbol: simulator.symbol,
        price: newPrice,
        priceChange,
        priceChangePercent,
        volume: Math.random() * 1000000, // Random volume
        timestamp: now
      }

      // Notify subscribers
      this.notifySubscribers(simulator.symbol, marketData)
    })
  }

  subscribe(callback: (symbol: string, data: MarketData) => void): () => void {
    this.subscribers.push(callback)
    
    // Return unsubscribe function
    return () => {
      const index = this.subscribers.indexOf(callback)
      if (index > -1) {
        this.subscribers.splice(index, 1)
      }
    }
  }

  private notifySubscribers(symbol: string, data: MarketData) {
    this.subscribers.forEach(callback => callback(symbol, data))
  }

  getCurrentPrice(symbol: string): number | null {
    const simulator = this.simulators.get(symbol)
    return simulator ? simulator.lastPrice : null
  }

  addSymbol(symbol: string, basePrice: number, volatility: number = 0.03) {
    if (!this.simulators.has(symbol)) {
      this.simulators.set(symbol, {
        symbol,
        basePrice,
        volatility,
        trend: (Math.random() - 0.5) * 0.001,
        lastPrice: basePrice,
        lastUpdate: Date.now()
      })
    }
  }

  removeSymbol(symbol: string) {
    this.simulators.delete(symbol)
  }
}

// Create singleton instance
export const marketDataSimulator = new MarketDataSimulator()
export default marketDataSimulator
