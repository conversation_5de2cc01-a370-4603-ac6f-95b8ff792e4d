/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"381f30465872\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkQ6XFxzdGVwLWJ5LXN0ZXBcXHRoZXBhcGVyYnVsbC0xNDRcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzODFmMzA0NjU4NzJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _app_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_ui_sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/sonner */ \"(rsc)/./components/ui/sonner.tsx\");\n/* harmony import */ var _components_ui_notification__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/notification */ \"(rsc)/./components/ui/notification.tsx\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./components/providers.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"ThePaperBull - Crypto Trading Platform\",\n    description: \"Advanced cryptocurrency futures trading platform\",\n    generator: \"v0.dev\",\n    metadataBase: new URL(\"https://thepaperbull.com\"),\n    openGraph: {\n        title: \"ThePaperBull - Advanced Crypto Trading Platform\",\n        description: \"Trade cryptocurrency futures with advanced charting, real-time data, and powerful trading tools.\",\n        images: [\n            {\n                url: \"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Screenshot%202025-04-21%20at%2000.01.09-1DI8PlTigyTXyu5nPuXVjFdbqYc5Av.png\",\n                width: 1200,\n                height: 630,\n                alt: \"ThePaperBull Trading Platform Interface\"\n            }\n        ],\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"ThePaperBull - Advanced Crypto Trading Platform\",\n        description: \"Trade cryptocurrency futures with advanced charting, real-time data, and powerful trading tools.\",\n        images: [\n            \"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Screenshot%202025-04-21%20at%2000.01.09-1DI8PlTigyTXyu5nPuXVjFdbqYc5Av.png\"\n        ],\n        creator: \"@thepaperbull\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_4__.Providers, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {}, void 0, false, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_notification__WEBPACK_IMPORTED_MODULE_3__.NotificationContainer, {}, void 0, false, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\layout.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\layout.tsx\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\layout.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\layout.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/loading.tsx":
/*!*************************!*\
  !*** ./app/loading.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\nfunction Loading() {\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbG9hZGluZy50c3giLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBO0lBQ3RCLE9BQU87QUFDVCIsInNvdXJjZXMiOlsiRDpcXHN0ZXAtYnktc3RlcFxcdGhlcGFwZXJidWxsLTE0NFxcYXBwXFxsb2FkaW5nLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2FkaW5nKCkge1xuICByZXR1cm4gbnVsbFxufVxuIl0sIm5hbWVzIjpbIkxvYWRpbmciXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ Providers)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Providers = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\step-by-step\\thepaperbull-144\\components\\providers.tsx",
"Providers",
);

/***/ }),

/***/ "(rsc)/./components/ui/notification.tsx":
/*!****************************************!*\
  !*** ./components/ui/notification.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Notification: () => (/* binding */ Notification),
/* harmony export */   NotificationContainer: () => (/* binding */ NotificationContainer),
/* harmony export */   notificationManager: () => (/* binding */ notificationManager),
/* harmony export */   useNotifications: () => (/* binding */ useNotifications)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Notification = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Notification() from the server but Notification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\step-by-step\\thepaperbull-144\\components\\ui\\notification.tsx",
"Notification",
);const notificationManager = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call notificationManager() from the server but notificationManager is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\step-by-step\\thepaperbull-144\\components\\ui\\notification.tsx",
"notificationManager",
);const useNotifications = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useNotifications() from the server but useNotifications is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\step-by-step\\thepaperbull-144\\components\\ui\\notification.tsx",
"useNotifications",
);const NotificationContainer = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call NotificationContainer() from the server but NotificationContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\step-by-step\\thepaperbull-144\\components\\ui\\notification.tsx",
"NotificationContainer",
);

/***/ }),

/***/ "(rsc)/./components/ui/sonner.tsx":
/*!**********************************!*\
  !*** ./components/ui/sonner.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ Toaster)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Toaster = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\step-by-step\\thepaperbull-144\\components\\ui\\sonner.tsx",
"Toaster",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5Cstep-by-step%5Cthepaperbull-144%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cstep-by-step%5Cthepaperbull-144&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5Cstep-by-step%5Cthepaperbull-144%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cstep-by-step%5Cthepaperbull-144&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/loading.tsx */ \"(rsc)/./app/loading.tsx\"));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\layout.tsx\"],\n'loading': [module2, \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\loading.tsx\"],\n'not-found': [module3, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module4, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module5, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5Cstep-by-step%5Cthepaperbull-144%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cstep-by-step%5Cthepaperbull-144&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cui%5C%5Cnotification.tsx%22%2C%22ids%22%3A%5B%22NotificationContainer%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cui%5C%5Cnotification.tsx%22%2C%22ids%22%3A%5B%22NotificationContainer%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers.tsx */ \"(rsc)/./components/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/notification.tsx */ \"(rsc)/./components/ui/notification.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/sonner.tsx */ \"(rsc)/./components/ui/sonner.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cui%5C%5Cnotification.tsx%22%2C%22ids%22%3A%5B%22NotificationContainer%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/auth-context */ \"(ssr)/./contexts/auth-context.tsx\");\n/* harmony import */ var _contexts_theme_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/theme-context */ \"(ssr)/./contexts/theme-context.tsx\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_auth_context__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_theme_context__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\components\\\\providers.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\components\\\\providers.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRXNEO0FBQ0U7QUFNakQsU0FBU0UsVUFBVSxFQUFFQyxRQUFRLEVBQWtCO0lBQ3BELHFCQUNFLDhEQUFDSCxnRUFBWUE7a0JBQ1gsNEVBQUNDLGtFQUFhQTtzQkFDWEU7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkQ6XFxzdGVwLWJ5LXN0ZXBcXHRoZXBhcGVyYnVsbC0xNDRcXGNvbXBvbmVudHNcXHByb3ZpZGVycy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gXCJAL2NvbnRleHRzL2F1dGgtY29udGV4dFwiXG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIH0gZnJvbSBcIkAvY29udGV4dHMvdGhlbWUtY29udGV4dFwiXG5cbmludGVyZmFjZSBQcm92aWRlcnNQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IFByb3ZpZGVyc1Byb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPEF1dGhQcm92aWRlcj5cbiAgICAgIDxUaGVtZVByb3ZpZGVyPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L1RoZW1lUHJvdmlkZXI+XG4gICAgPC9BdXRoUHJvdmlkZXI+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJBdXRoUHJvdmlkZXIiLCJUaGVtZVByb3ZpZGVyIiwiUHJvdmlkZXJzIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/notification.tsx":
/*!****************************************!*\
  !*** ./components/ui/notification.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Notification: () => (/* binding */ Notification),\n/* harmony export */   NotificationContainer: () => (/* binding */ NotificationContainer),\n/* harmony export */   notificationManager: () => (/* binding */ notificationManager),\n/* harmony export */   useNotifications: () => (/* binding */ useNotifications)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Notification,notificationManager,useNotifications,NotificationContainer auto */ \n\n\n\nconst notificationIcons = {\n    success: _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    error: _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    warning: _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    info: _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n};\nconst notificationStyles = {\n    success: \"border-green-200 bg-green-50 text-green-900 dark:border-green-800 dark:bg-green-950 dark:text-green-100\",\n    error: \"border-red-200 bg-red-50 text-red-900 dark:border-red-800 dark:bg-red-950 dark:text-red-100\",\n    warning: \"border-yellow-200 bg-yellow-50 text-yellow-900 dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-100\",\n    info: \"border-blue-200 bg-blue-50 text-blue-900 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-100\"\n};\nconst positionStyles = {\n    \"top-right\": \"top-4 right-4\",\n    \"top-left\": \"top-4 left-4\",\n    \"bottom-right\": \"bottom-4 right-4\",\n    \"bottom-left\": \"bottom-4 left-4\",\n    \"top-center\": \"top-4 left-1/2 transform -translate-x-1/2\",\n    \"bottom-center\": \"bottom-4 left-1/2 transform -translate-x-1/2\"\n};\nfunction Notification({ id, type = \"info\", title, message, duration = 4000, onClose, closable = true, position = \"top-right\" }) {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLeaving, setIsLeaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const Icon = notificationIcons[type];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Notification.useEffect\": ()=>{\n            if (duration > 0) {\n                const timer = setTimeout({\n                    \"Notification.useEffect.timer\": ()=>{\n                        handleClose();\n                    }\n                }[\"Notification.useEffect.timer\"], duration);\n                return ({\n                    \"Notification.useEffect\": ()=>clearTimeout(timer)\n                })[\"Notification.useEffect\"];\n            }\n        }\n    }[\"Notification.useEffect\"], [\n        duration\n    ]);\n    const handleClose = ()=>{\n        setIsLeaving(true);\n        setTimeout(()=>{\n            setIsVisible(false);\n            onClose?.();\n        }, 300) // Animation duration\n        ;\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed z-50 w-full max-w-sm p-4 border rounded-lg shadow-lg backdrop-blur-sm\", \"transition-all duration-300 ease-in-out\", !isLeaving && \"animate-slideInFromTop\", isLeaving && \"animate-slideOutToRight\", notificationStyles[type], positionStyles[position]),\n        style: {\n            animation: !isLeaving ? \"slideInFromTop 0.3s ease-out\" : \"slideOutToRight 0.3s ease-in\"\n        },\n        role: \"alert\",\n        \"aria-live\": \"polite\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: \"h-5 w-5 mt-0.5 flex-shrink-0\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\components\\\\ui\\\\notification.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium text-sm mb-1\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\components\\\\ui\\\\notification.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\components\\\\ui\\\\notification.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\components\\\\ui\\\\notification.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this),\n                closable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleClose,\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-shrink-0 p-1 rounded-md transition-colors duration-200\", \"hover:bg-black/10 dark:hover:bg-white/10\", \"focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current\", \"text-current/70 hover:text-current\"),\n                    \"aria-label\": \"Close notification\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\components\\\\ui\\\\notification.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\components\\\\ui\\\\notification.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\components\\\\ui\\\\notification.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\components\\\\ui\\\\notification.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n// Notification Manager for programmatic usage\nclass NotificationManager {\n    generateId() {\n        return `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n    }\n    notify() {\n        this.listeners.forEach((listener)=>listener());\n    }\n    show(notification) {\n        const id = this.generateId();\n        const notificationWithId = {\n            ...notification,\n            id,\n            onClose: ()=>this.remove(id)\n        };\n        this.notifications.set(id, notificationWithId);\n        this.notify();\n        return id;\n    }\n    remove(id) {\n        this.notifications.delete(id);\n        this.notify();\n    }\n    clear() {\n        this.notifications.clear();\n        this.notify();\n    }\n    getAll() {\n        return Array.from(this.notifications.values());\n    }\n    subscribe(listener) {\n        this.listeners.add(listener);\n        return ()=>this.listeners.delete(listener);\n    }\n    // Convenience methods\n    success(message, options) {\n        return this.show({\n            ...options,\n            type: \"success\",\n            message\n        });\n    }\n    error(message, options) {\n        return this.show({\n            ...options,\n            type: \"error\",\n            message\n        });\n    }\n    warning(message, options) {\n        return this.show({\n            ...options,\n            type: \"warning\",\n            message\n        });\n    }\n    info(message, options) {\n        return this.show({\n            ...options,\n            type: \"info\",\n            message\n        });\n    }\n    constructor(){\n        this.notifications = new Map();\n        this.listeners = new Set();\n    }\n}\nconst notificationManager = new NotificationManager();\n// Hook for using notifications in React components\nfunction useNotifications() {\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useNotifications.useEffect\": ()=>{\n            const updateNotifications = {\n                \"useNotifications.useEffect.updateNotifications\": ()=>{\n                    setNotifications(notificationManager.getAll());\n                }\n            }[\"useNotifications.useEffect.updateNotifications\"];\n            updateNotifications();\n            const unsubscribe = notificationManager.subscribe(updateNotifications);\n            return unsubscribe;\n        }\n    }[\"useNotifications.useEffect\"], []);\n    return {\n        notifications,\n        show: notificationManager.show.bind(notificationManager),\n        remove: notificationManager.remove.bind(notificationManager),\n        clear: notificationManager.clear.bind(notificationManager),\n        success: notificationManager.success.bind(notificationManager),\n        error: notificationManager.error.bind(notificationManager),\n        warning: notificationManager.warning.bind(notificationManager),\n        info: notificationManager.info.bind(notificationManager)\n    };\n}\n// Notification Container Component\nfunction NotificationContainer() {\n    const { notifications } = useNotifications();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Notification, {\n                ...notification\n            }, notification.id, false, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\components\\\\ui\\\\notification.tsx\",\n                lineNumber: 223,\n                columnNumber: 9\n            }, this))\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/notification.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/sonner.tsx":
/*!**********************************!*\
  !*** ./components/ui/sonner.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nconst Toaster = ({ ...props })=>{\n    const { theme = \"system\" } = (0,next_themes__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n        theme: theme,\n        className: \"toaster group\",\n        closeButton: true,\n        duration: 4000,\n        position: \"top-right\",\n        expand: true,\n        richColors: true,\n        toastOptions: {\n            classNames: {\n                toast: \"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg group-[.toaster]:rounded-lg\",\n                description: \"group-[.toast]:text-muted-foreground\",\n                actionButton: \"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground\",\n                cancelButton: \"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground\",\n                closeButton: \"group-[.toast]:bg-background group-[.toast]:text-foreground group-[.toast]:border-border group-[.toast]:hover:bg-muted group-[.toast]:focus:ring-2 group-[.toast]:focus:ring-ring group-[.toast]:focus:ring-offset-2\"\n            }\n        },\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\components\\\\ui\\\\sonner.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/sonner.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/auth-context.tsx":
/*!***********************************!*\
  !*** ./contexts/auth-context.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./lib/firebase.ts\");\n/* harmony import */ var _lib_auth_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth-utils */ \"(ssr)/./lib/auth-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, {\n                \"AuthProvider.useEffect.unsubscribe\": (user)=>{\n                    setUser(user);\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect.unsubscribe\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>unsubscribe()\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const handleSignIn = async (email, password)=>{\n        try {\n            const user = await (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_4__.signInWithEmail)(email, password);\n            return user;\n        } catch (error) {\n            console.error('Sign in error:', error);\n            throw error;\n        }\n    };\n    const handleSignOut = async ()=>{\n        try {\n            await (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_4__.signOutUser)();\n        } catch (error) {\n            console.error('Sign out error:', error);\n            throw error;\n        }\n    };\n    const handleGoogleSignIn = async ()=>{\n        try {\n            const user = await (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_4__.signInWithGoogle)();\n            return user;\n        } catch (error) {\n            console.error('Google sign in error:', error);\n            throw error;\n        }\n    };\n    const value = {\n        user,\n        loading,\n        signIn: handleSignIn,\n        signOut: handleSignOut,\n        signInWithGoogle: handleGoogleSignIn\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\contexts\\\\auth-context.tsx\",\n        lineNumber: 68,\n        columnNumber: 10\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/auth-context.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/theme-context.tsx":
/*!************************************!*\
  !*** ./contexts/theme-context.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_user_preferences_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/user-preferences-service */ \"(ssr)/./lib/user-preferences-service.ts\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [colorScheme, setColorScheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"emerald\");\n    const [resolvedTheme, setResolvedTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize theme and color scheme from user preferences service\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            setMounted(true);\n            // Subscribe to user preferences\n            const unsubscribe = _lib_user_preferences_service__WEBPACK_IMPORTED_MODULE_2__[\"default\"].subscribe({\n                \"ThemeProvider.useEffect.unsubscribe\": (preferences)=>{\n                    setTheme(preferences.theme);\n                    setColorScheme(preferences.colorScheme);\n                }\n            }[\"ThemeProvider.useEffect.unsubscribe\"]);\n            return unsubscribe;\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    // Apply theme and color scheme to document\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            // Apply theme to document\n            if (theme === \"dark\" || theme === \"system\" && window.matchMedia(\"(prefers-color-scheme: dark)\").matches) {\n                document.documentElement.classList.add(\"dark\");\n                setResolvedTheme(\"dark\");\n            } else {\n                document.documentElement.classList.remove(\"dark\");\n                setResolvedTheme(\"light\");\n            }\n            // Apply color scheme to document\n            document.documentElement.setAttribute(\"data-color-scheme\", colorScheme);\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme,\n        colorScheme,\n        mounted\n    ]);\n    // Listen for system theme changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            if (theme !== \"system\") return;\n            const mediaQuery = window.matchMedia(\"(prefers-color-scheme: dark)\");\n            const handleChange = {\n                \"ThemeProvider.useEffect.handleChange\": ()=>{\n                    if (mediaQuery.matches) {\n                        document.documentElement.classList.add(\"dark\");\n                        setResolvedTheme(\"dark\");\n                    } else {\n                        document.documentElement.classList.remove(\"dark\");\n                        setResolvedTheme(\"light\");\n                    }\n                }\n            }[\"ThemeProvider.useEffect.handleChange\"];\n            mediaQuery.addEventListener(\"change\", handleChange);\n            return ({\n                \"ThemeProvider.useEffect\": ()=>mediaQuery.removeEventListener(\"change\", handleChange)\n            })[\"ThemeProvider.useEffect\"];\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme,\n        mounted\n    ]);\n    // Avoid hydration mismatch\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    // Theme change handlers that update user preferences\n    const handleThemeChange = (newTheme)=>{\n        setTheme(newTheme);\n        _lib_user_preferences_service__WEBPACK_IMPORTED_MODULE_2__[\"default\"].updateTheme(newTheme);\n    };\n    const handleColorSchemeChange = (newColorScheme)=>{\n        setColorScheme(newColorScheme);\n        _lib_user_preferences_service__WEBPACK_IMPORTED_MODULE_2__[\"default\"].updateColorScheme(newColorScheme);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            setTheme: handleThemeChange,\n            colorScheme,\n            setColorScheme: handleColorSchemeChange,\n            resolvedTheme\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\contexts\\\\theme-context.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/theme-context.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/auth-utils.ts":
/*!***************************!*\
  !*** ./lib/auth-utils.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAuthErrorMessage: () => (/* binding */ getAuthErrorMessage),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   resetPassword: () => (/* binding */ resetPassword),\n/* harmony export */   signInWithEmail: () => (/* binding */ signInWithEmail),\n/* harmony export */   signInWithGoogle: () => (/* binding */ signInWithGoogle),\n/* harmony export */   signOutUser: () => (/* binding */ signOutUser),\n/* harmony export */   signUpWithEmail: () => (/* binding */ signUpWithEmail)\n/* harmony export */ });\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./lib/firebase.ts\");\n\n\n// Google Auth Provider\nconst googleProvider = new firebase_auth__WEBPACK_IMPORTED_MODULE_0__.GoogleAuthProvider();\ngoogleProvider.setCustomParameters({\n    prompt: 'select_account'\n});\n// Sign up with email and password\nconst signUpWithEmail = async (email, password, displayName)=>{\n    try {\n        const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.createUserWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, email, password);\n        // Update display name if provided\n        if (displayName && userCredential.user) {\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.updateProfile)(userCredential.user, {\n                displayName: displayName\n            });\n        }\n        return userCredential.user;\n    } catch (error) {\n        console.error('Sign up error:', error);\n        throw new Error(getAuthErrorMessage(error.code));\n    }\n};\n// Sign in with email and password\nconst signInWithEmail = async (email, password)=>{\n    try {\n        const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signInWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, email, password);\n        return userCredential.user;\n    } catch (error) {\n        console.error('Sign in error:', error);\n        throw new Error(getAuthErrorMessage(error.code));\n    }\n};\n// Sign in with Google\nconst signInWithGoogle = async ()=>{\n    try {\n        const result = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signInWithPopup)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, googleProvider);\n        return result.user;\n    } catch (error) {\n        console.error('Google sign in error:', error);\n        // Don't throw an error for user-cancelled actions\n        if (error.code === 'auth/popup-closed-by-user' || error.code === 'auth/cancelled-popup-request') {\n            throw error // Re-throw the original error so the UI can handle it appropriately\n            ;\n        }\n        throw new Error(getAuthErrorMessage(error.code));\n    }\n};\n// Sign out\nconst signOutUser = async ()=>{\n    try {\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signOut)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth);\n    } catch (error) {\n        console.error('Sign out error:', error);\n        throw new Error('Failed to sign out');\n    }\n};\n// Reset password\nconst resetPassword = async (email)=>{\n    try {\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.sendPasswordResetEmail)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, email);\n    } catch (error) {\n        console.error('Password reset error:', error);\n        throw new Error(getAuthErrorMessage(error.code));\n    }\n};\n// Get user-friendly error messages\nconst getAuthErrorMessage = (errorCode)=>{\n    switch(errorCode){\n        case 'auth/user-not-found':\n            return 'No account found with this email address. Please check your email or sign up for a new account.';\n        case 'auth/wrong-password':\n            return 'Incorrect password. Please try again or reset your password.';\n        case 'auth/invalid-credential':\n            return 'Invalid email or password. Please check your credentials and try again.';\n        case 'auth/email-already-in-use':\n            return 'An account with this email already exists. Please sign in instead.';\n        case 'auth/weak-password':\n            return 'Password should be at least 6 characters long.';\n        case 'auth/invalid-email':\n            return 'Please enter a valid email address.';\n        case 'auth/user-disabled':\n            return 'This account has been disabled. Please contact support.';\n        case 'auth/too-many-requests':\n            return 'Too many failed attempts. Please try again later or reset your password.';\n        case 'auth/popup-closed-by-user':\n            return 'Sign-in popup was closed. Please try again.';\n        case 'auth/popup-blocked':\n            return 'Sign-in popup was blocked. Please allow popups and try again.';\n        case 'auth/network-request-failed':\n            return 'Network error. Please check your internet connection and try again.';\n        case 'auth/cancelled-popup-request':\n            return 'Sign-in was cancelled.';\n        case 'auth/account-exists-with-different-credential':\n            return 'An account already exists with the same email address but different sign-in credentials.';\n        case 'auth/operation-not-allowed':\n            return 'This sign-in method is not enabled. Please contact support.';\n        case 'auth/invalid-api-key':\n            return 'Invalid API key. Please check your Firebase configuration.';\n        case 'auth/app-deleted':\n            return 'Firebase app has been deleted. Please check your configuration.';\n        case 'auth/invalid-user-token':\n            return 'User token is invalid. Please sign in again.';\n        case 'auth/user-token-expired':\n            return 'User token has expired. Please sign in again.';\n        default:\n            return `Authentication error: ${errorCode || 'Unknown error'}`;\n    }\n};\n// Check if user is authenticated\nconst isAuthenticated = ()=>{\n    return _firebase__WEBPACK_IMPORTED_MODULE_1__.auth.currentUser !== null;\n};\n// Get current user\nconst getCurrentUser = ()=>{\n    return _firebase__WEBPACK_IMPORTED_MODULE_1__.auth.currentUser;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/auth-utils.ts\n");

/***/ }),

/***/ "(ssr)/./lib/firebase.ts":
/*!*************************!*\
  !*** ./lib/firebase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analytics: () => (/* binding */ analytics),\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   realtimeDb: () => (/* binding */ realtimeDb)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(ssr)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_database__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/database */ \"(ssr)/./node_modules/firebase/database/dist/index.mjs\");\n/* harmony import */ var firebase_analytics__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/analytics */ \"(ssr)/./node_modules/firebase/analytics/dist/index.mjs\");\n// Firebase client-side configuration\n\n\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyBYTl5SiY2ARvKUCiMBxf8zNUBQRu3hg1s\",\n    authDomain: \"thepaperbull-144.firebaseapp.com\",\n    databaseURL: \"https://thepaperbull-144-default-rtdb.firebaseio.com/\",\n    projectId: \"thepaperbull-144\",\n    storageBucket: \"thepaperbull-144.firebasestorage.app\",\n    messagingSenderId: \"540770032311\",\n    appId: \"1:540770032311:web:54b0d4ec1715779408cb32\",\n    measurementId: \"G-5KTY505WKQ\"\n};\n// Initialize Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length === 0 ? (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig) : (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)()[0];\n// Initialize Firebase Auth\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\n// Initialize Firestore\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\n// Initialize Realtime Database\nconst realtimeDb = (0,firebase_database__WEBPACK_IMPORTED_MODULE_3__.getDatabase)(app);\n// Initialize Analytics (only in browser)\nconst analytics =  false ? 0 : null;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/firebase.ts\n");

/***/ }),

/***/ "(ssr)/./lib/firestore-service.ts":
/*!**********************************!*\
  !*** ./lib/firestore-service.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   firestoreService: () => (/* binding */ firestoreService)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./lib/firebase.ts\");\n// Firestore service for managing user data\n\n\n// Removed trading types - using Realtime Database for trading data\n// Default values\nconst DEFAULT_USER_BALANCE = 10000;\nconst FREE_TIER_MAX_BALANCE = 50000;\n// Trading interfaces removed - using Realtime Database for trading data\nclass FirestoreService {\n    // User Profile Management\n    async createUserProfile(userId, email, name) {\n        const now = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)();\n        const userProfile = {\n            email,\n            name,\n            avatar: 'bull-trader',\n            createdAt: now,\n            subscription: {\n                type: 'free',\n                status: 'active',\n                startDate: now,\n                features: [\n                    'basic_trading',\n                    'paper_trading',\n                    'basic_charts'\n                ]\n            },\n            balance: {\n                current: DEFAULT_USER_BALANCE,\n                maximum: FREE_TIER_MAX_BALANCE,\n                currency: 'USDT',\n                lastUpdated: now\n            },\n            settings: {\n                theme: 'system',\n                notifications: {\n                    email: true,\n                    push: true,\n                    trading_alerts: true,\n                    price_alerts: true,\n                    news_updates: false\n                },\n                trading: {\n                    default_leverage: 10,\n                    risk_management: true,\n                    auto_close_positions: false,\n                    confirmation_dialogs: true\n                }\n            }\n        };\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userId), userProfile);\n        // Create initial deposit transaction\n        await this.addTransaction(userId, {\n            type: 'deposit',\n            amount: DEFAULT_USER_BALANCE,\n            balance_before: 0,\n            balance_after: DEFAULT_USER_BALANCE,\n            description: 'Initial balance'\n        });\n        return {\n            id: userId,\n            ...userProfile\n        };\n    }\n    async getUserProfile(userId) {\n        try {\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userId));\n            if (userDoc.exists()) {\n                return {\n                    id: userId,\n                    ...userDoc.data()\n                };\n            }\n            return null;\n        } catch (error) {\n            console.error('Error getting user profile:', error);\n            return null;\n        }\n    }\n    async updateUserProfile(userId, updates) {\n        try {\n            // Only update balance.lastUpdated if we're actually updating balance\n            const updateData = {\n                ...updates\n            };\n            if (updates.balance) {\n                updateData['balance.lastUpdated'] = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)();\n            }\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userId), updateData);\n        } catch (error) {\n            console.error('Error updating user profile:', error);\n            throw error;\n        }\n    }\n    async updateUserBalance(userId, newBalance) {\n        try {\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userId), {\n                'balance.current': newBalance,\n                'balance.lastUpdated': (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)()\n            });\n        } catch (error) {\n            console.error('Error updating user balance:', error);\n            throw error;\n        }\n    }\n    // Transaction Management\n    async addTransaction(userId, transaction) {\n        try {\n            const transactionData = {\n                ...transaction,\n                userId,\n                timestamp: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)()\n            };\n            const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'transactions'), transactionData);\n            return docRef.id;\n        } catch (error) {\n            console.error('Error adding transaction:', error);\n            throw error;\n        }\n    }\n    async getUserTransactions(userId, limit = 50) {\n        try {\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'transactions'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('timestamp', 'desc'));\n            return new Promise((resolve, reject)=>{\n                const unsubscribe = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.onSnapshot)(q, (snapshot)=>{\n                    const transactions = snapshot.docs.map((doc)=>({\n                            id: doc.id,\n                            ...doc.data()\n                        }));\n                    resolve(transactions.slice(0, limit));\n                }, reject);\n            });\n        } catch (error) {\n            console.error('Error getting user transactions:', error);\n            return [];\n        }\n    }\n    // Position management removed - using Realtime Database for trading data\n    // Order management removed - using Realtime Database for trading data\n    // Trade management removed - using Realtime Database for trading data\n    // Real-time subscriptions\n    subscribeToUserProfile(userId, callback) {\n        return (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.onSnapshot)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', userId), (doc)=>{\n            if (doc.exists()) {\n                callback({\n                    id: userId,\n                    ...doc.data()\n                });\n            } else {\n                callback(null);\n            }\n        });\n    }\n}\nconst firestoreService = new FirestoreService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/firestore-service.ts\n");

/***/ }),

/***/ "(ssr)/./lib/notification-service.ts":
/*!*************************************!*\
  !*** ./lib/notification-service.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   notificationService: () => (/* binding */ notificationService)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./lib/firebase.ts\");\n\n\nclass NotificationService {\n    // Initialize the service with a user\n    initialize(user) {\n        this.currentUser = user;\n        // Clean up previous subscription\n        if (this.unsubscribeFirestore) {\n            this.unsubscribeFirestore();\n            this.unsubscribeFirestore = null;\n        }\n        if (user) {\n            this.subscribeToUserNotifications(user.uid);\n        } else {\n            this.notifications = [];\n            this.notifySubscribers();\n        }\n    }\n    // Subscribe to user notifications from Firestore\n    subscribeToUserNotifications(userId) {\n        const notificationsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'notifications');\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(notificationsRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'));\n        this.unsubscribeFirestore = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.onSnapshot)(q, (snapshot)=>{\n            // Merge with local state to avoid overwriting temporary notifications\n            const firestoreNotifications = snapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data()\n                }));\n            const tempNotifications = this.notifications.filter((n)=>n.id.startsWith('temp_'));\n            // Replace non-temp notifications with Firestore data\n            this.notifications = [\n                ...tempNotifications,\n                ...firestoreNotifications\n            ];\n            this.notifySubscribers();\n        }, (error)=>{\n            console.error('Error listening to notifications:', error);\n        });\n    }\n    // Subscribe to notification updates\n    subscribe(callback) {\n        this.subscribers.push(callback);\n        // Immediately call with current notifications\n        callback(this.notifications);\n        // Return unsubscribe function\n        return ()=>{\n            const index = this.subscribers.indexOf(callback);\n            if (index > -1) {\n                this.subscribers.splice(index, 1);\n            }\n        };\n    }\n    // Notify all subscribers\n    notifySubscribers() {\n        this.subscribers.forEach((callback)=>callback(this.notifications));\n    }\n    // Get all notifications\n    getNotifications() {\n        return this.notifications;\n    }\n    // Get unread notifications count\n    getUnreadCount() {\n        return this.notifications.filter((n)=>!n.read).length;\n    }\n    // Mark notification as read\n    async markAsRead(notificationId) {\n        if (!this.currentUser) return;\n        try {\n            const notificationRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'notifications', notificationId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(notificationRef, {\n                read: true\n            });\n        } catch (error) {\n            console.error('Error marking notification as read:', error);\n        }\n    }\n    // Mark all notifications as read\n    async markAllAsRead() {\n        if (!this.currentUser) return;\n        try {\n            const unreadNotifications = this.notifications.filter((n)=>!n.read);\n            const promises = unreadNotifications.map((notification)=>this.markAsRead(notification.id));\n            await Promise.all(promises);\n        } catch (error) {\n            console.error('Error marking all notifications as read:', error);\n        }\n    }\n    // Create a new notification\n    async createNotification(userId, title, message, type = 'info', data) {\n        try {\n            // Add to local state immediately for instant UI feedback\n            const tempNotification = {\n                id: `temp_${Date.now()}`,\n                userId,\n                title,\n                message,\n                type,\n                read: false,\n                createdAt: new Date(),\n                data: data || null\n            };\n            this.notifications.unshift(tempNotification);\n            this.notifySubscribers();\n            // Add to Firestore in background\n            const notificationsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'notifications');\n            const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)(notificationsRef, {\n                userId,\n                title,\n                message,\n                type,\n                read: false,\n                createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)(),\n                data: data || null\n            });\n            // Update local notification with real ID\n            const notificationIndex = this.notifications.findIndex((n)=>n.id === tempNotification.id);\n            if (notificationIndex !== -1) {\n                this.notifications[notificationIndex].id = docRef.id;\n            }\n        } catch (error) {\n            console.error('Error creating notification:', error);\n        }\n    }\n    // Create system notifications for trading events\n    async createTradeNotification(userId, tradeType, details) {\n        let title = '';\n        let message = '';\n        switch(tradeType){\n            case 'order_filled':\n                title = 'Order Filled';\n                message = `Your ${details.side} order for ${details.symbol} has been filled at $${details.price}`;\n                break;\n            case 'position_opened':\n                title = 'Position Opened';\n                message = `New ${details.side} position opened for ${details.symbol} - Size: ${details.size}`;\n                break;\n            case 'position_closed':\n                title = 'Position Closed';\n                message = `${details.symbol} position closed - P&L: ${details.pnl > 0 ? '+' : ''}$${details.pnl.toFixed(2)}`;\n                break;\n            case 'stop_loss':\n                title = 'Stop Loss Triggered';\n                message = `Stop loss triggered for ${details.symbol} at $${details.price}`;\n                break;\n            case 'take_profit':\n                title = 'Take Profit Hit';\n                message = `Take profit reached for ${details.symbol} at $${details.price}`;\n                break;\n        }\n        await this.createNotification(userId, title, message, 'trade', details);\n    }\n    // Create welcome notification for new users\n    async createWelcomeNotification(userId) {\n        await this.createNotification(userId, 'Welcome to ThePaperBull!', 'Start your paper trading journey with $10,000 virtual balance. Practice trading without risk!', 'success');\n    }\n    // Cleanup\n    destroy() {\n        if (this.unsubscribeFirestore) {\n            this.unsubscribeFirestore();\n        }\n        this.subscribers = [];\n        this.notifications = [];\n    }\n    constructor(){\n        this.subscribers = [];\n        this.unsubscribeFirestore = null;\n        this.currentUser = null;\n        this.notifications = [];\n    }\n}\n// Export singleton instance\nconst notificationService = new NotificationService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (notificationService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/notification-service.ts\n");

/***/ }),

/***/ "(ssr)/./lib/user-preferences-service.ts":
/*!*****************************************!*\
  !*** ./lib/user-preferences-service.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   userPreferencesService: () => (/* binding */ userPreferencesService)\n/* harmony export */ });\n/* harmony import */ var _firestore_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./firestore-service */ \"(ssr)/./lib/firestore-service.ts\");\n/* harmony import */ var _user_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./user-service */ \"(ssr)/./lib/user-service.ts\");\n\n\nclass UserPreferencesService {\n    constructor(){\n        this.preferences = {\n            favoriteMarkets: [],\n            theme: 'light',\n            colorScheme: 'emerald'\n        };\n        this.subscribers = [];\n        this.isInitialized = false;\n        // Initialize when user service is ready\n        _user_service__WEBPACK_IMPORTED_MODULE_1__[\"default\"].subscribe((user)=>{\n            if (user) {\n                this.loadUserPreferences(user.id);\n            } else {\n                this.resetPreferences();\n            }\n        });\n    }\n    // Subscribe to preference changes\n    subscribe(callback) {\n        this.subscribers.push(callback);\n        // Immediately call with current preferences if initialized\n        if (this.isInitialized) {\n            callback(this.preferences);\n        }\n        return ()=>{\n            const index = this.subscribers.indexOf(callback);\n            if (index > -1) {\n                this.subscribers.splice(index, 1);\n            }\n        };\n    }\n    notifySubscribers() {\n        this.subscribers.forEach((callback)=>callback(this.preferences));\n    }\n    async loadUserPreferences(userId) {\n        try {\n            const userDoc = await _firestore_service__WEBPACK_IMPORTED_MODULE_0__.firestoreService.getUserProfile(userId);\n            if (userDoc?.preferences) {\n                this.preferences = {\n                    ...this.preferences,\n                    ...userDoc.preferences\n                };\n            }\n            this.isInitialized = true;\n            this.notifySubscribers();\n        } catch (error) {\n            console.error('Error loading user preferences:', error);\n            this.isInitialized = true;\n            this.notifySubscribers();\n        }\n    }\n    resetPreferences() {\n        this.preferences = {\n            favoriteMarkets: [],\n            theme: 'light',\n            colorScheme: 'emerald'\n        };\n        this.isInitialized = false;\n    }\n    // Get current preferences\n    getPreferences() {\n        return {\n            ...this.preferences\n        };\n    }\n    // Update favorite markets\n    async updateFavoriteMarkets(favoriteMarkets) {\n        const user = _user_service__WEBPACK_IMPORTED_MODULE_1__[\"default\"].getFirebaseUser();\n        if (!user) return;\n        try {\n            this.preferences.favoriteMarkets = favoriteMarkets;\n            await _firestore_service__WEBPACK_IMPORTED_MODULE_0__.firestoreService.updateUserProfile(user.uid, {\n                preferences: this.preferences\n            });\n            this.notifySubscribers();\n        } catch (error) {\n            console.error('Error updating favorite markets:', error);\n        }\n    }\n    // Update theme\n    async updateTheme(theme) {\n        const user = _user_service__WEBPACK_IMPORTED_MODULE_1__[\"default\"].getFirebaseUser();\n        if (!user) {\n            // Store locally if not authenticated\n            this.preferences.theme = theme;\n            this.notifySubscribers();\n            return;\n        }\n        try {\n            this.preferences.theme = theme;\n            await _firestore_service__WEBPACK_IMPORTED_MODULE_0__.firestoreService.updateUserProfile(user.uid, {\n                preferences: this.preferences\n            });\n            this.notifySubscribers();\n        } catch (error) {\n            console.error('Error updating theme:', error);\n        }\n    }\n    // Update color scheme\n    async updateColorScheme(colorScheme) {\n        const user = _user_service__WEBPACK_IMPORTED_MODULE_1__[\"default\"].getFirebaseUser();\n        if (!user) {\n            // Store locally if not authenticated\n            this.preferences.colorScheme = colorScheme;\n            this.notifySubscribers();\n            return;\n        }\n        try {\n            this.preferences.colorScheme = colorScheme;\n            await _firestore_service__WEBPACK_IMPORTED_MODULE_0__.firestoreService.updateUserProfile(user.uid, {\n                preferences: this.preferences\n            });\n            this.notifySubscribers();\n        } catch (error) {\n            console.error('Error updating color scheme:', error);\n        }\n    }\n    // Update remembered username\n    async updateRememberedUsername(username) {\n        try {\n            if (username) {\n                this.preferences.rememberedUsername = username;\n            } else {\n                delete this.preferences.rememberedUsername;\n            }\n            // For remembered username, we can store locally since it's used before authentication\n            if (false) {}\n            this.notifySubscribers();\n        } catch (error) {\n            console.error('Error updating remembered username:', error);\n        }\n    }\n    // Get remembered username (fallback to localStorage for compatibility)\n    getRememberedUsername() {\n        if (this.preferences.rememberedUsername) {\n            return this.preferences.rememberedUsername;\n        }\n        if (false) {}\n        return null;\n    }\n    // Toggle favorite market\n    async toggleFavoriteMarket(symbol) {\n        const currentFavorites = this.preferences.favoriteMarkets;\n        const newFavorites = currentFavorites.includes(symbol) ? currentFavorites.filter((s)=>s !== symbol) : [\n            ...currentFavorites,\n            symbol\n        ];\n        await this.updateFavoriteMarkets(newFavorites);\n    }\n    // Check if market is favorite\n    isFavoriteMarket(symbol) {\n        return this.preferences.favoriteMarkets.includes(symbol);\n    }\n    // Get favorite markets\n    getFavoriteMarkets() {\n        return [\n            ...this.preferences.favoriteMarkets\n        ];\n    }\n    // Get theme\n    getTheme() {\n        return this.preferences.theme;\n    }\n    // Get color scheme\n    getColorScheme() {\n        return this.preferences.colorScheme;\n    }\n}\n// Export singleton instance\nconst userPreferencesService = new UserPreferencesService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (userPreferencesService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXNlci1wcmVmZXJlbmNlcy1zZXJ2aWNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBc0Q7QUFDZDtBQVN4QyxNQUFNRTtJQVVKQyxhQUFjO2FBVE5DLGNBQStCO1lBQ3JDQyxpQkFBaUIsRUFBRTtZQUNuQkMsT0FBTztZQUNQQyxhQUFhO1FBQ2Y7YUFFUUMsY0FBMEQsRUFBRTthQUM1REMsZ0JBQWdCO1FBR3RCLHdDQUF3QztRQUN4Q1IscURBQVdBLENBQUNTLFNBQVMsQ0FBQyxDQUFDQztZQUNyQixJQUFJQSxNQUFNO2dCQUNSLElBQUksQ0FBQ0MsbUJBQW1CLENBQUNELEtBQUtFLEVBQUU7WUFDbEMsT0FBTztnQkFDTCxJQUFJLENBQUNDLGdCQUFnQjtZQUN2QjtRQUNGO0lBQ0Y7SUFFQSxrQ0FBa0M7SUFDbENKLFVBQVVLLFFBQWdELEVBQWM7UUFDdEUsSUFBSSxDQUFDUCxXQUFXLENBQUNRLElBQUksQ0FBQ0Q7UUFFdEIsMkRBQTJEO1FBQzNELElBQUksSUFBSSxDQUFDTixhQUFhLEVBQUU7WUFDdEJNLFNBQVMsSUFBSSxDQUFDWCxXQUFXO1FBQzNCO1FBRUEsT0FBTztZQUNMLE1BQU1hLFFBQVEsSUFBSSxDQUFDVCxXQUFXLENBQUNVLE9BQU8sQ0FBQ0g7WUFDdkMsSUFBSUUsUUFBUSxDQUFDLEdBQUc7Z0JBQ2QsSUFBSSxDQUFDVCxXQUFXLENBQUNXLE1BQU0sQ0FBQ0YsT0FBTztZQUNqQztRQUNGO0lBQ0Y7SUFFUUcsb0JBQW9CO1FBQzFCLElBQUksQ0FBQ1osV0FBVyxDQUFDYSxPQUFPLENBQUNOLENBQUFBLFdBQVlBLFNBQVMsSUFBSSxDQUFDWCxXQUFXO0lBQ2hFO0lBRUEsTUFBY1Esb0JBQW9CVSxNQUFjLEVBQUU7UUFDaEQsSUFBSTtZQUNGLE1BQU1DLFVBQVUsTUFBTXZCLGdFQUFnQkEsQ0FBQ3dCLGNBQWMsQ0FBQ0Y7WUFDdEQsSUFBSUMsU0FBU25CLGFBQWE7Z0JBQ3hCLElBQUksQ0FBQ0EsV0FBVyxHQUFHO29CQUNqQixHQUFHLElBQUksQ0FBQ0EsV0FBVztvQkFDbkIsR0FBR21CLFFBQVFuQixXQUFXO2dCQUN4QjtZQUNGO1lBQ0EsSUFBSSxDQUFDSyxhQUFhLEdBQUc7WUFDckIsSUFBSSxDQUFDVyxpQkFBaUI7UUFDeEIsRUFBRSxPQUFPSyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxtQ0FBbUNBO1lBQ2pELElBQUksQ0FBQ2hCLGFBQWEsR0FBRztZQUNyQixJQUFJLENBQUNXLGlCQUFpQjtRQUN4QjtJQUNGO0lBRVFOLG1CQUFtQjtRQUN6QixJQUFJLENBQUNWLFdBQVcsR0FBRztZQUNqQkMsaUJBQWlCLEVBQUU7WUFDbkJDLE9BQU87WUFDUEMsYUFBYTtRQUNmO1FBQ0EsSUFBSSxDQUFDRSxhQUFhLEdBQUc7SUFDdkI7SUFFQSwwQkFBMEI7SUFDMUJrQixpQkFBa0M7UUFDaEMsT0FBTztZQUFFLEdBQUcsSUFBSSxDQUFDdkIsV0FBVztRQUFDO0lBQy9CO0lBRUEsMEJBQTBCO0lBQzFCLE1BQU13QixzQkFBc0J2QixlQUF5QixFQUFpQjtRQUNwRSxNQUFNTSxPQUFPVixxREFBV0EsQ0FBQzRCLGVBQWU7UUFDeEMsSUFBSSxDQUFDbEIsTUFBTTtRQUVYLElBQUk7WUFDRixJQUFJLENBQUNQLFdBQVcsQ0FBQ0MsZUFBZSxHQUFHQTtZQUNuQyxNQUFNTCxnRUFBZ0JBLENBQUM4QixpQkFBaUIsQ0FBQ25CLEtBQUtvQixHQUFHLEVBQUU7Z0JBQ2pEM0IsYUFBYSxJQUFJLENBQUNBLFdBQVc7WUFDL0I7WUFDQSxJQUFJLENBQUNnQixpQkFBaUI7UUFDeEIsRUFBRSxPQUFPSyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxvQ0FBb0NBO1FBQ3BEO0lBQ0Y7SUFFQSxlQUFlO0lBQ2YsTUFBTU8sWUFBWTFCLEtBQWtDLEVBQWlCO1FBQ25FLE1BQU1LLE9BQU9WLHFEQUFXQSxDQUFDNEIsZUFBZTtRQUN4QyxJQUFJLENBQUNsQixNQUFNO1lBQ1QscUNBQXFDO1lBQ3JDLElBQUksQ0FBQ1AsV0FBVyxDQUFDRSxLQUFLLEdBQUdBO1lBQ3pCLElBQUksQ0FBQ2MsaUJBQWlCO1lBQ3RCO1FBQ0Y7UUFFQSxJQUFJO1lBQ0YsSUFBSSxDQUFDaEIsV0FBVyxDQUFDRSxLQUFLLEdBQUdBO1lBQ3pCLE1BQU1OLGdFQUFnQkEsQ0FBQzhCLGlCQUFpQixDQUFDbkIsS0FBS29CLEdBQUcsRUFBRTtnQkFDakQzQixhQUFhLElBQUksQ0FBQ0EsV0FBVztZQUMvQjtZQUNBLElBQUksQ0FBQ2dCLGlCQUFpQjtRQUN4QixFQUFFLE9BQU9LLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHlCQUF5QkE7UUFDekM7SUFDRjtJQUVBLHNCQUFzQjtJQUN0QixNQUFNUSxrQkFBa0IxQixXQUFtQixFQUFpQjtRQUMxRCxNQUFNSSxPQUFPVixxREFBV0EsQ0FBQzRCLGVBQWU7UUFDeEMsSUFBSSxDQUFDbEIsTUFBTTtZQUNULHFDQUFxQztZQUNyQyxJQUFJLENBQUNQLFdBQVcsQ0FBQ0csV0FBVyxHQUFHQTtZQUMvQixJQUFJLENBQUNhLGlCQUFpQjtZQUN0QjtRQUNGO1FBRUEsSUFBSTtZQUNGLElBQUksQ0FBQ2hCLFdBQVcsQ0FBQ0csV0FBVyxHQUFHQTtZQUMvQixNQUFNUCxnRUFBZ0JBLENBQUM4QixpQkFBaUIsQ0FBQ25CLEtBQUtvQixHQUFHLEVBQUU7Z0JBQ2pEM0IsYUFBYSxJQUFJLENBQUNBLFdBQVc7WUFDL0I7WUFDQSxJQUFJLENBQUNnQixpQkFBaUI7UUFDeEIsRUFBRSxPQUFPSyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxnQ0FBZ0NBO1FBQ2hEO0lBQ0Y7SUFFQSw2QkFBNkI7SUFDN0IsTUFBTVMseUJBQXlCQyxRQUF1QixFQUFpQjtRQUNyRSxJQUFJO1lBQ0YsSUFBSUEsVUFBVTtnQkFDWixJQUFJLENBQUMvQixXQUFXLENBQUNnQyxrQkFBa0IsR0FBR0Q7WUFDeEMsT0FBTztnQkFDTCxPQUFPLElBQUksQ0FBQy9CLFdBQVcsQ0FBQ2dDLGtCQUFrQjtZQUM1QztZQUVBLHNGQUFzRjtZQUN0RixJQUFJLEtBQTZCLEVBQUUsRUFNbEM7WUFFRCxJQUFJLENBQUNoQixpQkFBaUI7UUFDeEIsRUFBRSxPQUFPSyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyx1Q0FBdUNBO1FBQ3ZEO0lBQ0Y7SUFFQSx1RUFBdUU7SUFDdkVlLHdCQUF1QztRQUNyQyxJQUFJLElBQUksQ0FBQ3BDLFdBQVcsQ0FBQ2dDLGtCQUFrQixFQUFFO1lBQ3ZDLE9BQU8sSUFBSSxDQUFDaEMsV0FBVyxDQUFDZ0Msa0JBQWtCO1FBQzVDO1FBRUEsSUFBSSxLQUE2QixFQUFFLEVBRWxDO1FBRUQsT0FBTztJQUNUO0lBRUEseUJBQXlCO0lBQ3pCLE1BQU1NLHFCQUFxQkMsTUFBYyxFQUFpQjtRQUN4RCxNQUFNQyxtQkFBbUIsSUFBSSxDQUFDeEMsV0FBVyxDQUFDQyxlQUFlO1FBQ3pELE1BQU13QyxlQUFlRCxpQkFBaUJFLFFBQVEsQ0FBQ0gsVUFDM0NDLGlCQUFpQkcsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxNQUFNTCxVQUNuQztlQUFJQztZQUFrQkQ7U0FBTztRQUVqQyxNQUFNLElBQUksQ0FBQ2YscUJBQXFCLENBQUNpQjtJQUNuQztJQUVBLDhCQUE4QjtJQUM5QkksaUJBQWlCTixNQUFjLEVBQVc7UUFDeEMsT0FBTyxJQUFJLENBQUN2QyxXQUFXLENBQUNDLGVBQWUsQ0FBQ3lDLFFBQVEsQ0FBQ0g7SUFDbkQ7SUFFQSx1QkFBdUI7SUFDdkJPLHFCQUErQjtRQUM3QixPQUFPO2VBQUksSUFBSSxDQUFDOUMsV0FBVyxDQUFDQyxlQUFlO1NBQUM7SUFDOUM7SUFFQSxZQUFZO0lBQ1o4QyxXQUF3QztRQUN0QyxPQUFPLElBQUksQ0FBQy9DLFdBQVcsQ0FBQ0UsS0FBSztJQUMvQjtJQUVBLG1CQUFtQjtJQUNuQjhDLGlCQUF5QjtRQUN2QixPQUFPLElBQUksQ0FBQ2hELFdBQVcsQ0FBQ0csV0FBVztJQUNyQztBQUNGO0FBRUEsNEJBQTRCO0FBQ3JCLE1BQU04Qyx5QkFBeUIsSUFBSW5ELHlCQUF3QjtBQUNsRSxpRUFBZW1ELHNCQUFzQkEsRUFBQSIsInNvdXJjZXMiOlsiRDpcXHN0ZXAtYnktc3RlcFxcdGhlcGFwZXJidWxsLTE0NFxcbGliXFx1c2VyLXByZWZlcmVuY2VzLXNlcnZpY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZmlyZXN0b3JlU2VydmljZSB9IGZyb20gJy4vZmlyZXN0b3JlLXNlcnZpY2UnXG5pbXBvcnQgdXNlclNlcnZpY2UgZnJvbSAnLi91c2VyLXNlcnZpY2UnXG5cbmludGVyZmFjZSBVc2VyUHJlZmVyZW5jZXMge1xuICBmYXZvcml0ZU1hcmtldHM6IHN0cmluZ1tdXG4gIHRoZW1lOiAnbGlnaHQnIHwgJ2RhcmsnIHwgJ3N5c3RlbSdcbiAgY29sb3JTY2hlbWU6IHN0cmluZ1xuICByZW1lbWJlcmVkVXNlcm5hbWU/OiBzdHJpbmdcbn1cblxuY2xhc3MgVXNlclByZWZlcmVuY2VzU2VydmljZSB7XG4gIHByaXZhdGUgcHJlZmVyZW5jZXM6IFVzZXJQcmVmZXJlbmNlcyA9IHtcbiAgICBmYXZvcml0ZU1hcmtldHM6IFtdLFxuICAgIHRoZW1lOiAnbGlnaHQnLFxuICAgIGNvbG9yU2NoZW1lOiAnZW1lcmFsZCdcbiAgfVxuXG4gIHByaXZhdGUgc3Vic2NyaWJlcnM6ICgocHJlZmVyZW5jZXM6IFVzZXJQcmVmZXJlbmNlcykgPT4gdm9pZClbXSA9IFtdXG4gIHByaXZhdGUgaXNJbml0aWFsaXplZCA9IGZhbHNlXG5cbiAgY29uc3RydWN0b3IoKSB7XG4gICAgLy8gSW5pdGlhbGl6ZSB3aGVuIHVzZXIgc2VydmljZSBpcyByZWFkeVxuICAgIHVzZXJTZXJ2aWNlLnN1YnNjcmliZSgodXNlcikgPT4ge1xuICAgICAgaWYgKHVzZXIpIHtcbiAgICAgICAgdGhpcy5sb2FkVXNlclByZWZlcmVuY2VzKHVzZXIuaWQpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aGlzLnJlc2V0UHJlZmVyZW5jZXMoKVxuICAgICAgfVxuICAgIH0pXG4gIH1cblxuICAvLyBTdWJzY3JpYmUgdG8gcHJlZmVyZW5jZSBjaGFuZ2VzXG4gIHN1YnNjcmliZShjYWxsYmFjazogKHByZWZlcmVuY2VzOiBVc2VyUHJlZmVyZW5jZXMpID0+IHZvaWQpOiAoKSA9PiB2b2lkIHtcbiAgICB0aGlzLnN1YnNjcmliZXJzLnB1c2goY2FsbGJhY2spXG4gICAgXG4gICAgLy8gSW1tZWRpYXRlbHkgY2FsbCB3aXRoIGN1cnJlbnQgcHJlZmVyZW5jZXMgaWYgaW5pdGlhbGl6ZWRcbiAgICBpZiAodGhpcy5pc0luaXRpYWxpemVkKSB7XG4gICAgICBjYWxsYmFjayh0aGlzLnByZWZlcmVuY2VzKVxuICAgIH1cblxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBjb25zdCBpbmRleCA9IHRoaXMuc3Vic2NyaWJlcnMuaW5kZXhPZihjYWxsYmFjaylcbiAgICAgIGlmIChpbmRleCA+IC0xKSB7XG4gICAgICAgIHRoaXMuc3Vic2NyaWJlcnMuc3BsaWNlKGluZGV4LCAxKVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgbm90aWZ5U3Vic2NyaWJlcnMoKSB7XG4gICAgdGhpcy5zdWJzY3JpYmVycy5mb3JFYWNoKGNhbGxiYWNrID0+IGNhbGxiYWNrKHRoaXMucHJlZmVyZW5jZXMpKVxuICB9XG5cbiAgcHJpdmF0ZSBhc3luYyBsb2FkVXNlclByZWZlcmVuY2VzKHVzZXJJZDogc3RyaW5nKSB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzZXJEb2MgPSBhd2FpdCBmaXJlc3RvcmVTZXJ2aWNlLmdldFVzZXJQcm9maWxlKHVzZXJJZClcbiAgICAgIGlmICh1c2VyRG9jPy5wcmVmZXJlbmNlcykge1xuICAgICAgICB0aGlzLnByZWZlcmVuY2VzID0ge1xuICAgICAgICAgIC4uLnRoaXMucHJlZmVyZW5jZXMsXG4gICAgICAgICAgLi4udXNlckRvYy5wcmVmZXJlbmNlc1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICB0aGlzLmlzSW5pdGlhbGl6ZWQgPSB0cnVlXG4gICAgICB0aGlzLm5vdGlmeVN1YnNjcmliZXJzKClcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyB1c2VyIHByZWZlcmVuY2VzOicsIGVycm9yKVxuICAgICAgdGhpcy5pc0luaXRpYWxpemVkID0gdHJ1ZVxuICAgICAgdGhpcy5ub3RpZnlTdWJzY3JpYmVycygpXG4gICAgfVxuICB9XG5cbiAgcHJpdmF0ZSByZXNldFByZWZlcmVuY2VzKCkge1xuICAgIHRoaXMucHJlZmVyZW5jZXMgPSB7XG4gICAgICBmYXZvcml0ZU1hcmtldHM6IFtdLFxuICAgICAgdGhlbWU6ICdsaWdodCcsXG4gICAgICBjb2xvclNjaGVtZTogJ2VtZXJhbGQnXG4gICAgfVxuICAgIHRoaXMuaXNJbml0aWFsaXplZCA9IGZhbHNlXG4gIH1cblxuICAvLyBHZXQgY3VycmVudCBwcmVmZXJlbmNlc1xuICBnZXRQcmVmZXJlbmNlcygpOiBVc2VyUHJlZmVyZW5jZXMge1xuICAgIHJldHVybiB7IC4uLnRoaXMucHJlZmVyZW5jZXMgfVxuICB9XG5cbiAgLy8gVXBkYXRlIGZhdm9yaXRlIG1hcmtldHNcbiAgYXN5bmMgdXBkYXRlRmF2b3JpdGVNYXJrZXRzKGZhdm9yaXRlTWFya2V0czogc3RyaW5nW10pOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBjb25zdCB1c2VyID0gdXNlclNlcnZpY2UuZ2V0RmlyZWJhc2VVc2VyKClcbiAgICBpZiAoIXVzZXIpIHJldHVyblxuXG4gICAgdHJ5IHtcbiAgICAgIHRoaXMucHJlZmVyZW5jZXMuZmF2b3JpdGVNYXJrZXRzID0gZmF2b3JpdGVNYXJrZXRzXG4gICAgICBhd2FpdCBmaXJlc3RvcmVTZXJ2aWNlLnVwZGF0ZVVzZXJQcm9maWxlKHVzZXIudWlkLCB7XG4gICAgICAgIHByZWZlcmVuY2VzOiB0aGlzLnByZWZlcmVuY2VzXG4gICAgICB9KVxuICAgICAgdGhpcy5ub3RpZnlTdWJzY3JpYmVycygpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIGZhdm9yaXRlIG1hcmtldHM6JywgZXJyb3IpXG4gICAgfVxuICB9XG5cbiAgLy8gVXBkYXRlIHRoZW1lXG4gIGFzeW5jIHVwZGF0ZVRoZW1lKHRoZW1lOiAnbGlnaHQnIHwgJ2RhcmsnIHwgJ3N5c3RlbScpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBjb25zdCB1c2VyID0gdXNlclNlcnZpY2UuZ2V0RmlyZWJhc2VVc2VyKClcbiAgICBpZiAoIXVzZXIpIHtcbiAgICAgIC8vIFN0b3JlIGxvY2FsbHkgaWYgbm90IGF1dGhlbnRpY2F0ZWRcbiAgICAgIHRoaXMucHJlZmVyZW5jZXMudGhlbWUgPSB0aGVtZVxuICAgICAgdGhpcy5ub3RpZnlTdWJzY3JpYmVycygpXG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgdGhpcy5wcmVmZXJlbmNlcy50aGVtZSA9IHRoZW1lXG4gICAgICBhd2FpdCBmaXJlc3RvcmVTZXJ2aWNlLnVwZGF0ZVVzZXJQcm9maWxlKHVzZXIudWlkLCB7XG4gICAgICAgIHByZWZlcmVuY2VzOiB0aGlzLnByZWZlcmVuY2VzXG4gICAgICB9KVxuICAgICAgdGhpcy5ub3RpZnlTdWJzY3JpYmVycygpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIHRoZW1lOicsIGVycm9yKVxuICAgIH1cbiAgfVxuXG4gIC8vIFVwZGF0ZSBjb2xvciBzY2hlbWVcbiAgYXN5bmMgdXBkYXRlQ29sb3JTY2hlbWUoY29sb3JTY2hlbWU6IHN0cmluZyk6IFByb21pc2U8dm9pZD4ge1xuICAgIGNvbnN0IHVzZXIgPSB1c2VyU2VydmljZS5nZXRGaXJlYmFzZVVzZXIoKVxuICAgIGlmICghdXNlcikge1xuICAgICAgLy8gU3RvcmUgbG9jYWxseSBpZiBub3QgYXV0aGVudGljYXRlZFxuICAgICAgdGhpcy5wcmVmZXJlbmNlcy5jb2xvclNjaGVtZSA9IGNvbG9yU2NoZW1lXG4gICAgICB0aGlzLm5vdGlmeVN1YnNjcmliZXJzKClcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICB0aGlzLnByZWZlcmVuY2VzLmNvbG9yU2NoZW1lID0gY29sb3JTY2hlbWVcbiAgICAgIGF3YWl0IGZpcmVzdG9yZVNlcnZpY2UudXBkYXRlVXNlclByb2ZpbGUodXNlci51aWQsIHtcbiAgICAgICAgcHJlZmVyZW5jZXM6IHRoaXMucHJlZmVyZW5jZXNcbiAgICAgIH0pXG4gICAgICB0aGlzLm5vdGlmeVN1YnNjcmliZXJzKClcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgY29sb3Igc2NoZW1lOicsIGVycm9yKVxuICAgIH1cbiAgfVxuXG4gIC8vIFVwZGF0ZSByZW1lbWJlcmVkIHVzZXJuYW1lXG4gIGFzeW5jIHVwZGF0ZVJlbWVtYmVyZWRVc2VybmFtZSh1c2VybmFtZTogc3RyaW5nIHwgbnVsbCk6IFByb21pc2U8dm9pZD4ge1xuICAgIHRyeSB7XG4gICAgICBpZiAodXNlcm5hbWUpIHtcbiAgICAgICAgdGhpcy5wcmVmZXJlbmNlcy5yZW1lbWJlcmVkVXNlcm5hbWUgPSB1c2VybmFtZVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgZGVsZXRlIHRoaXMucHJlZmVyZW5jZXMucmVtZW1iZXJlZFVzZXJuYW1lXG4gICAgICB9XG4gICAgICBcbiAgICAgIC8vIEZvciByZW1lbWJlcmVkIHVzZXJuYW1lLCB3ZSBjYW4gc3RvcmUgbG9jYWxseSBzaW5jZSBpdCdzIHVzZWQgYmVmb3JlIGF1dGhlbnRpY2F0aW9uXG4gICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgaWYgKHVzZXJuYW1lKSB7XG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3JlbWVtYmVyZWRVc2VybmFtZScsIHVzZXJuYW1lKVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdyZW1lbWJlcmVkVXNlcm5hbWUnKVxuICAgICAgICB9XG4gICAgICB9XG4gICAgICBcbiAgICAgIHRoaXMubm90aWZ5U3Vic2NyaWJlcnMoKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyByZW1lbWJlcmVkIHVzZXJuYW1lOicsIGVycm9yKVxuICAgIH1cbiAgfVxuXG4gIC8vIEdldCByZW1lbWJlcmVkIHVzZXJuYW1lIChmYWxsYmFjayB0byBsb2NhbFN0b3JhZ2UgZm9yIGNvbXBhdGliaWxpdHkpXG4gIGdldFJlbWVtYmVyZWRVc2VybmFtZSgpOiBzdHJpbmcgfCBudWxsIHtcbiAgICBpZiAodGhpcy5wcmVmZXJlbmNlcy5yZW1lbWJlcmVkVXNlcm5hbWUpIHtcbiAgICAgIHJldHVybiB0aGlzLnByZWZlcmVuY2VzLnJlbWVtYmVyZWRVc2VybmFtZVxuICAgIH1cbiAgICBcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIHJldHVybiBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgncmVtZW1iZXJlZFVzZXJuYW1lJylcbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIG51bGxcbiAgfVxuXG4gIC8vIFRvZ2dsZSBmYXZvcml0ZSBtYXJrZXRcbiAgYXN5bmMgdG9nZ2xlRmF2b3JpdGVNYXJrZXQoc3ltYm9sOiBzdHJpbmcpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBjb25zdCBjdXJyZW50RmF2b3JpdGVzID0gdGhpcy5wcmVmZXJlbmNlcy5mYXZvcml0ZU1hcmtldHNcbiAgICBjb25zdCBuZXdGYXZvcml0ZXMgPSBjdXJyZW50RmF2b3JpdGVzLmluY2x1ZGVzKHN5bWJvbClcbiAgICAgID8gY3VycmVudEZhdm9yaXRlcy5maWx0ZXIocyA9PiBzICE9PSBzeW1ib2wpXG4gICAgICA6IFsuLi5jdXJyZW50RmF2b3JpdGVzLCBzeW1ib2xdXG4gICAgXG4gICAgYXdhaXQgdGhpcy51cGRhdGVGYXZvcml0ZU1hcmtldHMobmV3RmF2b3JpdGVzKVxuICB9XG5cbiAgLy8gQ2hlY2sgaWYgbWFya2V0IGlzIGZhdm9yaXRlXG4gIGlzRmF2b3JpdGVNYXJrZXQoc3ltYm9sOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgICByZXR1cm4gdGhpcy5wcmVmZXJlbmNlcy5mYXZvcml0ZU1hcmtldHMuaW5jbHVkZXMoc3ltYm9sKVxuICB9XG5cbiAgLy8gR2V0IGZhdm9yaXRlIG1hcmtldHNcbiAgZ2V0RmF2b3JpdGVNYXJrZXRzKCk6IHN0cmluZ1tdIHtcbiAgICByZXR1cm4gWy4uLnRoaXMucHJlZmVyZW5jZXMuZmF2b3JpdGVNYXJrZXRzXVxuICB9XG5cbiAgLy8gR2V0IHRoZW1lXG4gIGdldFRoZW1lKCk6ICdsaWdodCcgfCAnZGFyaycgfCAnc3lzdGVtJyB7XG4gICAgcmV0dXJuIHRoaXMucHJlZmVyZW5jZXMudGhlbWVcbiAgfVxuXG4gIC8vIEdldCBjb2xvciBzY2hlbWVcbiAgZ2V0Q29sb3JTY2hlbWUoKTogc3RyaW5nIHtcbiAgICByZXR1cm4gdGhpcy5wcmVmZXJlbmNlcy5jb2xvclNjaGVtZVxuICB9XG59XG5cbi8vIEV4cG9ydCBzaW5nbGV0b24gaW5zdGFuY2VcbmV4cG9ydCBjb25zdCB1c2VyUHJlZmVyZW5jZXNTZXJ2aWNlID0gbmV3IFVzZXJQcmVmZXJlbmNlc1NlcnZpY2UoKVxuZXhwb3J0IGRlZmF1bHQgdXNlclByZWZlcmVuY2VzU2VydmljZVxuIl0sIm5hbWVzIjpbImZpcmVzdG9yZVNlcnZpY2UiLCJ1c2VyU2VydmljZSIsIlVzZXJQcmVmZXJlbmNlc1NlcnZpY2UiLCJjb25zdHJ1Y3RvciIsInByZWZlcmVuY2VzIiwiZmF2b3JpdGVNYXJrZXRzIiwidGhlbWUiLCJjb2xvclNjaGVtZSIsInN1YnNjcmliZXJzIiwiaXNJbml0aWFsaXplZCIsInN1YnNjcmliZSIsInVzZXIiLCJsb2FkVXNlclByZWZlcmVuY2VzIiwiaWQiLCJyZXNldFByZWZlcmVuY2VzIiwiY2FsbGJhY2siLCJwdXNoIiwiaW5kZXgiLCJpbmRleE9mIiwic3BsaWNlIiwibm90aWZ5U3Vic2NyaWJlcnMiLCJmb3JFYWNoIiwidXNlcklkIiwidXNlckRvYyIsImdldFVzZXJQcm9maWxlIiwiZXJyb3IiLCJjb25zb2xlIiwiZ2V0UHJlZmVyZW5jZXMiLCJ1cGRhdGVGYXZvcml0ZU1hcmtldHMiLCJnZXRGaXJlYmFzZVVzZXIiLCJ1cGRhdGVVc2VyUHJvZmlsZSIsInVpZCIsInVwZGF0ZVRoZW1lIiwidXBkYXRlQ29sb3JTY2hlbWUiLCJ1cGRhdGVSZW1lbWJlcmVkVXNlcm5hbWUiLCJ1c2VybmFtZSIsInJlbWVtYmVyZWRVc2VybmFtZSIsImxvY2FsU3RvcmFnZSIsInNldEl0ZW0iLCJyZW1vdmVJdGVtIiwiZ2V0UmVtZW1iZXJlZFVzZXJuYW1lIiwiZ2V0SXRlbSIsInRvZ2dsZUZhdm9yaXRlTWFya2V0Iiwic3ltYm9sIiwiY3VycmVudEZhdm9yaXRlcyIsIm5ld0Zhdm9yaXRlcyIsImluY2x1ZGVzIiwiZmlsdGVyIiwicyIsImlzRmF2b3JpdGVNYXJrZXQiLCJnZXRGYXZvcml0ZU1hcmtldHMiLCJnZXRUaGVtZSIsImdldENvbG9yU2NoZW1lIiwidXNlclByZWZlcmVuY2VzU2VydmljZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/user-preferences-service.ts\n");

/***/ }),

/***/ "(ssr)/./lib/user-service.ts":
/*!*****************************!*\
  !*** ./lib/user-service.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   userService: () => (/* binding */ userService)\n/* harmony export */ });\n/* harmony import */ var _firestore_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./firestore-service */ \"(ssr)/./lib/firestore-service.ts\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./lib/firebase.ts\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var _notification_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notification-service */ \"(ssr)/./lib/notification-service.ts\");\n/* harmony import */ var _types_user__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../types/user */ \"(ssr)/./types/user.ts\");\n\n\n\n\n\n// Convert Firestore UserProfile to legacy User type for compatibility\nfunction convertProfileToUser(profile) {\n    return {\n        id: profile.id,\n        email: profile.email,\n        name: profile.name,\n        createdAt: profile.createdAt?.toMillis?.() || Date.now(),\n        subscription: {\n            type: profile.subscription.type,\n            status: profile.subscription.status,\n            startDate: profile.subscription.startDate?.toMillis?.() || Date.now(),\n            endDate: profile.subscription.endDate?.toMillis?.(),\n            features: profile.subscription.features\n        },\n        balance: {\n            current: profile.balance.current,\n            maximum: profile.balance.maximum,\n            currency: profile.balance.currency,\n            lastUpdated: profile.balance.lastUpdated?.toMillis?.() || Date.now(),\n            transactions: [] // Will be loaded separately\n        },\n        settings: profile.settings\n    };\n}\nclass UserService {\n    constructor(){\n        this.currentUser = null;\n        this.firebaseUser = null;\n        this.subscribers = [];\n        this.unsubscribeAuth = null;\n        this.unsubscribeProfile = null;\n        this.transactions = [];\n        this.balanceCache = null;\n        this.BALANCE_CACHE_DURATION = 5000 // 5 seconds cache\n        ;\n        this.initializeAuth();\n    }\n    initializeAuth() {\n        // Listen to Firebase Auth state changes\n        this.unsubscribeAuth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, async (firebaseUser)=>{\n            this.firebaseUser = firebaseUser;\n            if (firebaseUser) {\n                // User is signed in, load their profile\n                await this.loadUserProfile(firebaseUser.uid);\n            } else {\n                // User is signed out\n                this.currentUser = null;\n                this.transactions = [];\n                if (this.unsubscribeProfile) {\n                    this.unsubscribeProfile();\n                    this.unsubscribeProfile = null;\n                }\n                this.notifySubscribers();\n            }\n        });\n    }\n    async loadUserProfile(userId) {\n        try {\n            // Check if user profile exists\n            let userProfile = await _firestore_service__WEBPACK_IMPORTED_MODULE_0__.firestoreService.getUserProfile(userId);\n            if (!userProfile && this.firebaseUser) {\n                // Create new user profile if it doesn't exist\n                userProfile = await _firestore_service__WEBPACK_IMPORTED_MODULE_0__.firestoreService.createUserProfile(userId, this.firebaseUser.email || '', this.firebaseUser.displayName || 'User');\n                // Create welcome notification for new users\n                if (userProfile) {\n                    await _notification_service__WEBPACK_IMPORTED_MODULE_3__[\"default\"].createWelcomeNotification(userId);\n                }\n            }\n            if (userProfile) {\n                this.currentUser = convertProfileToUser(userProfile);\n                // Load transactions\n                this.transactions = await _firestore_service__WEBPACK_IMPORTED_MODULE_0__.firestoreService.getUserTransactions(userId);\n                this.currentUser.balance.transactions = this.transactions.map((t)=>({\n                        id: t.id,\n                        type: t.type,\n                        amount: t.amount,\n                        balance_before: t.balance_before,\n                        balance_after: t.balance_after,\n                        description: t.description,\n                        timestamp: t.timestamp?.toMillis?.() || Date.now()\n                    }));\n                // Subscribe to real-time updates\n                if (this.unsubscribeProfile) {\n                    this.unsubscribeProfile();\n                }\n                this.unsubscribeProfile = _firestore_service__WEBPACK_IMPORTED_MODULE_0__.firestoreService.subscribeToUserProfile(userId, (profile)=>{\n                    if (profile) {\n                        this.currentUser = convertProfileToUser(profile);\n                        this.notifySubscribers();\n                    }\n                });\n                this.notifySubscribers();\n            }\n        } catch (error) {\n            console.error('Error loading user profile:', error);\n            // Check if it's a permissions error\n            if (error.code === 'permission-denied') {\n                console.error('Permission denied - Firestore security rules may need to be updated');\n                // Create a minimal user profile with available data\n                if (this.firebaseUser) {\n                    this.currentUser = {\n                        id: this.firebaseUser.uid,\n                        email: this.firebaseUser.email || '',\n                        name: this.firebaseUser.displayName || 'User',\n                        avatar: 'bull-trader',\n                        balance: {\n                            current: 10000,\n                            maximum: 50000,\n                            currency: 'USDT',\n                            lastUpdated: Date.now(),\n                            transactions: []\n                        },\n                        subscription: {\n                            type: 'free',\n                            status: 'active'\n                        },\n                        settings: {},\n                        createdAt: Date.now(),\n                        lastLoginAt: Date.now()\n                    };\n                    this.notifySubscribers();\n                }\n            } else {\n                // For other errors, set user to null\n                this.currentUser = null;\n                this.notifySubscribers();\n            }\n        }\n    }\n    notifySubscribers() {\n        // Invalidate balance cache when user data changes\n        this.balanceCache = null;\n        this.subscribers.forEach((callback)=>callback(this.currentUser));\n    }\n    // Public methods\n    getUser() {\n        return this.currentUser;\n    }\n    getUserBalance() {\n        // Use cached balance if available and not expired\n        const now = Date.now();\n        if (this.balanceCache && now - this.balanceCache.timestamp < this.BALANCE_CACHE_DURATION) {\n            return this.balanceCache.value;\n        }\n        // Get fresh balance and cache it\n        const balance = this.currentUser?.balance?.current || 10000;\n        this.balanceCache = {\n            value: balance,\n            timestamp: now\n        };\n        return balance;\n    }\n    getMaxBalance() {\n        return this.currentUser?.balance.maximum || _types_user__WEBPACK_IMPORTED_MODULE_4__.FREE_TIER_MAX_BALANCE;\n    }\n    getAvailableFunds() {\n        if (!this.currentUser) return 0;\n        return this.currentUser.balance.maximum - this.currentUser.balance.current;\n    }\n    canAddFunds(amount) {\n        if (!this.currentUser) {\n            return {\n                canAdd: false,\n                reason: 'User not found'\n            };\n        }\n        if (amount <= 0) {\n            return {\n                canAdd: false,\n                reason: 'Amount must be positive'\n            };\n        }\n        const newBalance = this.currentUser.balance.current + amount;\n        if (newBalance > this.currentUser.balance.maximum) {\n            return {\n                canAdd: false,\n                reason: `Would exceed maximum balance of ${this.currentUser.balance.maximum.toLocaleString()} USDT`\n            };\n        }\n        return {\n            canAdd: true\n        };\n    }\n    async addFunds(request) {\n        if (!this.currentUser || !this.firebaseUser) {\n            return {\n                success: false,\n                new_balance: 0,\n                requires_subscription: false,\n                message: 'User not found'\n            };\n        }\n        const canAddResult = this.canAddFunds(request.amount);\n        if (!canAddResult.canAdd) {\n            // Check if subscription upgrade would allow this\n            const wouldFitWithUpgrade = this.currentUser.balance.current + request.amount <= _types_user__WEBPACK_IMPORTED_MODULE_4__.SUBSCRIPTION_PLANS.premium.maxBalance;\n            return {\n                success: false,\n                new_balance: this.currentUser.balance.current,\n                requires_subscription: wouldFitWithUpgrade,\n                message: canAddResult.reason || 'Cannot add funds'\n            };\n        }\n        try {\n            // Add the funds\n            const balanceBefore = this.currentUser.balance.current;\n            const balanceAfter = balanceBefore + request.amount;\n            // Update balance in Firestore\n            await _firestore_service__WEBPACK_IMPORTED_MODULE_0__.firestoreService.updateUserBalance(this.firebaseUser.uid, balanceAfter);\n            // Create transaction record in Firestore\n            await _firestore_service__WEBPACK_IMPORTED_MODULE_0__.firestoreService.addTransaction(this.firebaseUser.uid, {\n                type: 'deposit',\n                amount: request.amount,\n                balance_before: balanceBefore,\n                balance_after: balanceAfter,\n                description: request.method === 'subscription_upgrade' ? 'Funds added via subscription' : 'Virtual funds added'\n            });\n            return {\n                success: true,\n                new_balance: balanceAfter,\n                requires_subscription: false,\n                message: `Successfully added ${request.amount.toLocaleString()} USDT to your account`\n            };\n        } catch (error) {\n            console.error('Error adding funds:', error);\n            return {\n                success: false,\n                new_balance: this.currentUser.balance.current,\n                requires_subscription: false,\n                message: 'Failed to add funds. Please try again.'\n            };\n        }\n    }\n    async upgradeSubscription(planType) {\n        if (!this.currentUser || !this.firebaseUser) return false;\n        try {\n            const plan = _types_user__WEBPACK_IMPORTED_MODULE_4__.SUBSCRIPTION_PLANS[planType];\n            await _firestore_service__WEBPACK_IMPORTED_MODULE_0__.firestoreService.updateUserProfile(this.firebaseUser.uid, {\n                subscription: {\n                    type: planType,\n                    status: 'active',\n                    startDate: new Date(),\n                    endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),\n                    features: plan.features\n                },\n                balance: {\n                    ...this.currentUser.balance,\n                    maximum: plan.maxBalance\n                }\n            });\n            return true;\n        } catch (error) {\n            console.error('Error upgrading subscription:', error);\n            return false;\n        }\n    }\n    async updateBalance(newBalance, transactionType, description) {\n        if (!this.currentUser || !this.firebaseUser) return;\n        try {\n            const balanceBefore = this.currentUser.balance.current;\n            // Update balance in Firestore\n            await _firestore_service__WEBPACK_IMPORTED_MODULE_0__.firestoreService.updateUserBalance(this.firebaseUser.uid, newBalance);\n            // Create transaction record in Firestore\n            await _firestore_service__WEBPACK_IMPORTED_MODULE_0__.firestoreService.addTransaction(this.firebaseUser.uid, {\n                type: transactionType,\n                amount: newBalance - balanceBefore,\n                balance_before: balanceBefore,\n                balance_after: newBalance,\n                description\n            });\n        } catch (error) {\n            console.error('Error updating balance:', error);\n        }\n    }\n    subscribe(callback) {\n        this.subscribers.push(callback);\n        // Immediately call the callback with current user state\n        callback(this.currentUser);\n        // Return unsubscribe function\n        return ()=>{\n            const index = this.subscribers.indexOf(callback);\n            if (index > -1) {\n                this.subscribers.splice(index, 1);\n            }\n        };\n    }\n    // Get Firebase user\n    getFirebaseUser() {\n        return this.firebaseUser;\n    }\n    // Cleanup method\n    destroy() {\n        if (this.unsubscribeAuth) {\n            this.unsubscribeAuth();\n        }\n        if (this.unsubscribeProfile) {\n            this.unsubscribeProfile();\n        }\n    }\n}\n// Export singleton instance\nconst userService = new UserService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (userService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/user-service.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJEOlxcc3RlcC1ieS1zdGVwXFx0aGVwYXBlcmJ1bGwtMTQ0XFxsaWJcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cui%5C%5Cnotification.tsx%22%2C%22ids%22%3A%5B%22NotificationContainer%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cui%5C%5Cnotification.tsx%22%2C%22ids%22%3A%5B%22NotificationContainer%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers.tsx */ \"(ssr)/./components/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/notification.tsx */ \"(ssr)/./components/ui/notification.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/sonner.tsx */ \"(ssr)/./components/ui/sonner.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cui%5C%5Cnotification.tsx%22%2C%22ids%22%3A%5B%22NotificationContainer%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./types/user.ts":
/*!***********************!*\
  !*** ./types/user.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_USER_BALANCE: () => (/* binding */ DEFAULT_USER_BALANCE),\n/* harmony export */   FREE_TIER_MAX_BALANCE: () => (/* binding */ FREE_TIER_MAX_BALANCE),\n/* harmony export */   SUBSCRIPTION_PLANS: () => (/* binding */ SUBSCRIPTION_PLANS)\n/* harmony export */ });\n// User management type definitions\n// Subscription plans\nconst SUBSCRIPTION_PLANS = {\n    free: {\n        type: 'free',\n        name: 'Free',\n        maxBalance: 50000,\n        price: 0,\n        features: {\n            maxBalance: 50000,\n            advancedAnalytics: false,\n            prioritySupport: false,\n            customIndicators: false,\n            apiAccess: false\n        }\n    },\n    premium: {\n        type: 'premium',\n        name: 'Premium',\n        maxBalance: 500000,\n        price: 29.99,\n        features: {\n            maxBalance: 500000,\n            advancedAnalytics: true,\n            prioritySupport: false,\n            customIndicators: true,\n            apiAccess: false\n        }\n    },\n    pro: {\n        type: 'pro',\n        name: 'Pro',\n        maxBalance: 1000000,\n        price: 99.99,\n        features: {\n            maxBalance: 1000000,\n            advancedAnalytics: true,\n            prioritySupport: true,\n            customIndicators: true,\n            apiAccess: true\n        }\n    }\n};\n// Default values\nconst DEFAULT_USER_BALANCE = 10000;\nconst FREE_TIER_MAX_BALANCE = 50000;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./types/user.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@firebase","vendor-chunks/@grpc","vendor-chunks/protobufjs","vendor-chunks/tailwind-merge","vendor-chunks/sonner","vendor-chunks/long","vendor-chunks/websocket-driver","vendor-chunks/@protobufjs","vendor-chunks/lucide-react","vendor-chunks/lodash.camelcase","vendor-chunks/tslib","vendor-chunks/websocket-extensions","vendor-chunks/faye-websocket","vendor-chunks/http-parser-js","vendor-chunks/idb","vendor-chunks/next-themes","vendor-chunks/safe-buffer","vendor-chunks/firebase","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5Cstep-by-step%5Cthepaperbull-144%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cstep-by-step%5Cthepaperbull-144&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();